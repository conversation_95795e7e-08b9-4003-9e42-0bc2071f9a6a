{"name": "@ijabo-backend/notifications", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "MIT", "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/websockets": "^10.4.15", "axios": "^1.8.4", "nodemailer": "^6.9.16", "resend": "^4.2.0", "socket.io": "^4.8.1"}, "devDependencies": {"@types/nodemailer": "^6.4.17"}, "packageManager": "yarn@4.5.3"}