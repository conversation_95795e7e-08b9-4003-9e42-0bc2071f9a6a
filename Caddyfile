{
    # Global options block
    admin 0.0.0.0:2019  # Disable admin endpoint for security
    auto_https disable_redirects
    acme_ca https://acme.zerossl.com/v2/DV90
    email <EMAIL>
    log {
        format json
        output stderr
    }
}

(security) {
    # Block common probe and attack paths
    @blocked_paths {
        path /.env /.git/* /.DS_Store /wp-* /_all_dbs /actuator/*
        path /server-status /debug/* /telescope/* /info.php
        path /ecp/* /login.action /config.json /.vscode/*
        path /server /about /v2/_catalog
        not path /api/documentation*  # Exclude Swagger documentation
    }
    respond @blocked_paths 404

    # Security headers
    header {
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-Content-Type-Options "nosniff"
        Referrer-Policy "strict-origin-when-cross-origin"
        X-Frame-Options "DENY"
        X-XSS-Protection "1; mode=block"
        # Modified CSP to allow Swagger UI to work
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:;"
    }
}

api.ijabo.strettch.dev {
    import security

    # Handle Swagger documentation specifically
    handle /api/documentation* {
        reverse_proxy core-service:4010 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}

            # WebSocket support
            header_up Connection {http.upgrade}
            header_up Upgrade {http.upgrade}
        }
    }

    # Regular API traffic
    reverse_proxy core-service:4010 {
        header_up Host {host}
        header_up X-Real-IP {remote_host}

        health_uri /api/v1/health
        health_interval 30s
        health_timeout 10s
        health_status 200

        transport http {
            keepalive 30s
            keepalive_idle_conns 100
        }
    }

    encode zstd gzip

    handle_path /static/* {
        header Cache-Control "public, max-age=31536000"
    }

}

ai.ijabo.strettch.dev {
    import security

    # Handle Swagger documentation specifically
    handle /api/documentation* {
        reverse_proxy web:8000 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}

            # WebSocket support
            header_up Connection {http.upgrade}
            header_up Upgrade {http.upgrade}
        }
    }

    # Regular API traffic
    reverse_proxy web:8000 {
        header_up Host {host}
        header_up X-Real-IP {remote_host}

        health_uri /health
        health_interval 30s
        health_timeout 10s
        health_status 200

        transport http {
            keepalive 30s
            keepalive_idle_conns 100
        }
    }

    encode zstd gzip

    handle_path /static/* {
        header Cache-Control "public, max-age=31536000"
    }

}

notif.ijabo.strettch.dev {
    import security

    # Handle Swagger documentation specifically
    handle /api/documentation* {
        reverse_proxy core-service:4020 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}

            # WebSocket support
            header_up Connection {http.upgrade}
            header_up Upgrade {http.upgrade}
        }
    }

    reverse_proxy notification-service:4020 {
        header_up Host {host}
        header_up X-Real-IP {remote_host}

        health_uri /api/v1/health
        health_interval 30s
        health_timeout 10s
        health_status 200

        transport http {
            keepalive 30s
            keepalive_idle_conns 100
        }
    }

    encode zstd gzip
}

intergration.ijabo.strettch.dev {
    import security

    # Handle Swagger documentation specifically
    handle /api/documentation* {
        reverse_proxy core-service:4030 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}

            # WebSocket support
            header_up Connection {http.upgrade}
            header_up Upgrade {http.upgrade}
        }
    }

    reverse_proxy integration-service:4030 {
        header_up Host {host}
        header_up X-Real-IP {remote_host}

        health_uri /api/v1/health
        health_interval 30s
        health_timeout 10s
        health_status 200

        transport http {
            keepalive 30s
            keepalive_idle_conns 100
        }
    }

    encode zstd gzip
}


hocus.ijabo.strettch.dev {
    import security

    reverse_proxy hocus-pocus-service:5021 {
        header_up Host {host}
        header_up X-Real-IP {remote_host}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}

        # WebSocket support (important)
        header_up Connection {>Connection}
        header_up Upgrade {>Upgrade}

        transport http {
            keepalive 30s
            keepalive_idle_conns 100
            versions "1.1"
        }
    }

    encode zstd gzip
}


storage.ijabo.strettch.dev {
    import security

    # Handle WebSocket connections for Minio console
    reverse_proxy minio:9000 {
        header_up Host {host}
        header_up X-Real-IP {remote_host}
        header_up X-Forwarded-Proto {scheme}

        transport http {
            keepalive 30s
            versions "1.1" "2"
        }
    }

    @websockets {
        header Connection *Upgrade*
        header Upgrade websocket
    }

    handle @websockets {
        reverse_proxy minio:9000 {
            trusted_proxies private_ranges
        }
    }

    encode zstd gzip
}

minio.ijabo.strettch.dev {
    import security

    # Handle WebSocket connections for Minio console
    reverse_proxy minio:9001 {
        header_up Host {host}
        header_up X-Real-IP {remote_host}
        header_up X-Forwarded-Proto {scheme}

        transport http {
            keepalive 30s
            versions "1.1" "2"
        }
    }

    @websockets {
        header Connection *Upgrade*
        header Upgrade websocket
    }

    handle @websockets {
        reverse_proxy minio:9001 {
            trusted_proxies private_ranges
        }
    }

    encode zstd gzip
}
