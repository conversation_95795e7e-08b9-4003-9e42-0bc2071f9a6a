import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Not, Repository } from 'typeorm';
import { ClientGrpc } from '@nestjs/microservices';
import { INTEGRATION_GRPC_PACKAGE } from '@app/common/constants/services-constants';
import { StudentsService } from '@integration-service/modules/student/students.service';
import { GrpcServices } from '@integration-service/common/constants/grpc.constants';
import { EUserRole } from './enums/user-role.enum';
import { TeacherService } from '@integration-service/modules/teacher/teachers.service';
import { Portfolio } from '../portfolio/entities/portfolio.entity';
import { Project } from '../project/entities/project.entity';
import { ECollaborStatus } from '../project/enums/collaborator-status.enum';
import { UserIntegrationService } from '@integration-service/modules/user/user.service';
import { JobService } from '../job/job.service';
import { ViewsDto } from '../portfolio/dto/view.dto';
import { EViewType } from '../portfolio/enums/view-type.enum';
import { EProjectVisibility } from '../project/enums/project-visibility.enum';
import { EportfolioStatus } from '../portfolio/enums/portfolio-status.enum';
import { EProjectType } from '../project/enums/project-type.enum';
import { EPortfolioCategory } from '../portfolio/enums/portfolio-category.enum';
import { EmploymentType } from '../job/enums/employment-type.enum';
import { ExperienceLevel } from '../job/enums/experience-level.enum';
import { EJobStatus } from '../job/enums/job-status.enum';
import { MINIMUM_PROFILES_ON_PUBLIC_PORTAL } from '@core-service/common/constants/all.constants';
import { EProjectStatus } from '../project/enums/project-status.enum';
import { concat } from 'lodash';
import { PortfolioService } from '../portfolio/portfolio.service';
import { EducationLevel } from './enums/education-level.enum';
import { ProjectService } from '../project/project.service';

@Injectable()
export class PublicService {
  private readonly integrationStudentService: StudentsService;
  private readonly integrationTeacherService: TeacherService;
  private readonly userIntegrationService: UserIntegrationService;

  constructor(
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(Portfolio)
    private readonly portfolioRepository: Repository<Portfolio>,
    @Inject(INTEGRATION_GRPC_PACKAGE) private readonly client: ClientGrpc,
    private readonly jobService: JobService,
    private readonly portfolioService: PortfolioService,
    private readonly projectService: ProjectService,
  ) {
    this.integrationStudentService = this.client.getService<StudentsService>(
      GrpcServices.STUDENT_SERVICE,
    );
    this.integrationTeacherService = this.client.getService<TeacherService>(
      GrpcServices.LECTURE_SERVICE,
    );

    this.userIntegrationService =
      this.client.getService<UserIntegrationService>(
        GrpcServices.USER_INTEGRATION_SERVICE,
      );
  }
  async countProjectView(projectId: string, uniqueId: string, user: User) {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
      select: ['id', 'views', 'owner'],
    });

    if (!project) {
      throw new NotFoundException('Project not found');
    }

    project.views = project.views || [];

    const hasViewed = project.views.some((view) => view.uniqueId === uniqueId);

    if (!hasViewed) {
      const newView: ViewsDto = {
        uniqueId,
        type: EViewType.VIEW,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      project.views.push(newView);
      await this.projectRepository.save(project);
    }

    return { success: true };
  }

  async countPortfolioView(portfolioId: string, uniqueId: string, user: User) {
    const portfolio = await this.portfolioRepository.findOne({
      where: { id: portfolioId },
      select: ['id', 'views', 'user'],
    });

    if (!portfolio) {
      throw new NotFoundException('Portfolio not found');
    }

    portfolio.views = portfolio.views || [];

    const hasViewed = portfolio.views.some(
      (view) => view.uniqueId === uniqueId,
    );

    if (!hasViewed) {
      const newView: ViewsDto = {
        uniqueId,
        type: EViewType.VIEW,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      portfolio.views.push(newView);
      await this.portfolioRepository.save(portfolio);
    }

    return { success: true };
  }

  async countProjectsByOwner(owner: User) {
    const [projects, count] = await this.projectRepository.findAndCount({
      where: [
        { owner: { id: owner.id } },
        {
          collaborators: {
            user: { id: owner.id },
            status: ECollaborStatus.APPROVED,
          },
        },
      ],
    });
    return count;
  }

  async getLandingStats() {
    const [combinedProjectsStats, combinedPortfoliosStats] = await Promise.all([
      this.projectService.getCombinedProjectsStats(),
      this.portfolioService.getCombinedPortfoliosStats(),
    ]);

    return {
      ...combinedProjectsStats,
      ...combinedPortfoliosStats,
    };
  }

  async countAllProjects(type?: EProjectType) {
    const query = this.projectRepository
      .createQueryBuilder('project')
      .where('project.status = :status', { status: EProjectStatus.PUBLISHED })
      .andWhere('project.visibility = :visibility', {
        visibility: EProjectVisibility.PUBLIC,
      });

    if (type) {
      query.andWhere('project.type = :type', { type: type });
    }
    return await query.getCount();
  }

  async getProjectVerificationGraph(
    year: number,
    collegeId?: string,
    ownerRole?: EUserRole,
  ) {
    const endDate = new Date(year, 11, 31);
    const startDate = new Date(year, 0, 1);

    const projectsQuery = this.projectRepository
      .createQueryBuilder('project')
      .leftJoinAndSelect('project.owner', 'owner')
      .leftJoinAndSelect('owner.college', 'college')
      .where('project.created_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('project.status IN (:...statuses)', {
        statuses: ['REQUESTED_CHANGES', 'DRAFT', 'QA_REVIEW'],
      });

    if (collegeId) {
      projectsQuery.andWhere('college.id = :collegeId', { collegeId });
    }

    if (ownerRole) {
      projectsQuery.andWhere('owner.role = :ownerRole', { ownerRole });
    }

    projectsQuery
      .select('EXTRACT(MONTH FROM project.created_at)', 'month')
      .addSelect('project.status', 'status')
      .addSelect('COUNT(DISTINCT project.id)', 'count')
      .groupBy('EXTRACT(MONTH FROM project.created_at)')
      .addGroupBy('project.status');

    const results = await projectsQuery.getRawMany();

    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      month: i + 1,
      month_name: new Date(2024, i, 1).toLocaleString('en-US', {
        month: 'short',
      }),
      requested_changes: 0,
      draft: 0,
      qa_review: 0,
    }));

    results.forEach((result) => {
      const monthIndex = Math.floor(result.month) - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        switch (result.status) {
          case 'REQUESTED_CHANGES':
            monthlyData[monthIndex].requested_changes = Number(result.count);
            break;
          case 'DRAFT':
            monthlyData[monthIndex].draft = Number(result.count);
            break;
          case 'QA_REVIEW':
            monthlyData[monthIndex].qa_review = Number(result.count);
            break;
        }
      }
    });

    return monthlyData;
  }

  async findJobsForPublic(
    filters: {
      keyword?: string;
      jobTypes?: EmploymentType[];
      skills?: string | string[];
      sectors?: string | string[];
      experienceLevels: ExperienceLevel[];
      status: EJobStatus;
    },
    page: number,
    limit: number,
  ) {
    const columnsToselect = [
      'job.id',
      'job.createdAt',
      'job.updatedAt',
      'job.title',
      'job.description',
      'job.status',
      'job.location',
      'job.deadline',
      'job.employmentType',
      'job.experienceLevel',
      'company.name',
      'company.sector',
      'company.logo',
      'sector.name',
    ];
    return await this.jobService.searchJobs(
      filters,
      columnsToselect,
      page,
      limit,
    );
  }
}
