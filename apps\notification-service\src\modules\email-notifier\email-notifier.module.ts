import { Modu<PERSON> } from '@nestjs/common';
import { EmailNotifierService } from './email-notifier.service';
import { EmailNotifierController } from './email-notifier.controller';
import { SlackModule } from '../slack/slack.module';
import { NotificationConfigService } from '@notification-service/configs/notification-config.service';
@Module({
  imports: [SlackModule],
  controllers: [EmailNotifierController],
  providers: [EmailNotifierService, NotificationConfigService],
})
export class EmailNotifierModule {}
