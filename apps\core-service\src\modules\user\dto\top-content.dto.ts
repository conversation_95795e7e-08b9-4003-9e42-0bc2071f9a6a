import { ApiProperty } from '@nestjs/swagger';

export class TopContentOwnerDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty()
  email: string;
}

export class TopPortfolioDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  owner: TopContentOwnerDto;

  @ApiProperty()
  viewCount: number;
}

export class TopProjectDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  owner: TopContentOwnerDto;

  @ApiProperty()
  viewCount: number;
}

export class TopContentResponseDto {
  @ApiProperty({ type: [TopPortfolioDto] })
  topPortfolios: TopPortfolioDto[];

  @ApiProperty({ type: [TopProjectDto] })
  topProjects: TopProjectDto[];
}
