import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseEntity } from '@app/common/database/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { College } from '../../college/entities/college.entity';
import { EUserRole } from '../enums/user-role.enum';
import { EUserStatus } from '../enums/user-status.enum';
import { Exclude } from 'class-transformer';
import { EGender } from '@app/common/enums';
import { Department } from '@core-service/modules/portfolio/entities/department.entity';
import { ResearchCluster } from '@core-service/modules/research/research-cluster/entities/cluster.entity';

@Entity({ name: 'user' })
export class User extends BaseEntity {
  @Column({ nullable: true }) // TODO: Change nullability to false  replacing the null values with actual ones in the database
  @ApiProperty()
  firstName: string;

  @Column({ nullable: true }) // TODO: Change nullability to false after replacing the null values with actual ones in the database
  @ApiProperty()
  lastName: string;

  @ManyToOne(() => Department, { nullable: true })
  @JoinColumn({ name: 'department_id' })
  @ApiProperty()
  department: Department;

  @Column()
  @Index({ unique: true })
  @ApiProperty()
  email: string;

  @Column({ nullable: true })
  @ApiProperty()
  nationalId: string;

  @Column({ nullable: true })
  @ApiProperty()
  @Exclude()
  password: string;
  @Column({ nullable: true })
  @ApiProperty()
  phoneNumber: string;

  @Column({ nullable: true })
  profilePicture: string;

  @Column({ nullable: true, name: 'registration_number' }) // TODO: Change nullability to false after replacing the null values with actual ones in the database
  @ApiProperty()
  registrationNumber: string;

  @Column({ type: 'enum', enum: EGender, nullable: true }) // TODO: change This to  nullable=false
  @ApiProperty()
  gender: EGender;

  @Column({
    nullable: false,
    type: 'enum',
    enum: EUserStatus,
    default: EUserStatus.ACTIVE,
  })
  @ApiProperty()
  status: EUserStatus;

  @Column({ nullable: true })
  @ApiProperty()
  dateOfBirth: Date;

  @ManyToOne(() => College, (college) => college, { nullable: true })
  @JoinColumn({ name: 'college' })
  @ApiProperty()
  college: College;

  @Column({ nullable: true })
  educationLevel: string;

  @Column({ nullable: true })
  graduationYear: string;

  @Column({ nullable: true })
  academicYear: string;

  @Column({ default: false, type: Boolean })
  twoFactorAuthEnabled: boolean;

  @Column({ default: false, type: Boolean })
  emailNotificationsEnabled: boolean;

  @Column({ default: false, type: Boolean })
  passwordResetRequired: boolean;

  @Column({ nullable: true })
  @ApiProperty()
  country: string;

  @Column({ type: 'enum', enum: EUserRole, nullable: false })
  @ApiProperty({ enum: EUserRole, example: EUserRole.STUDENT })
  role: EUserRole;

  @Column({ nullable: true })
  bio: string;

  @ManyToOne(() => ResearchCluster, (cluster) => cluster.members, {
    nullable: true,
  })
  @JoinColumn({ name: 'research_cluster_id' })
  @ApiProperty()
  researchCluster: ResearchCluster;
}
