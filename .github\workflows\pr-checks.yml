name: Pull Request Checks

on:
  pull_request:
    branches: [post-launch]

jobs:
  validate:
    runs-on: self-hosted
    strategy:
      matrix:
        service: [core-service, notification-service, integration-service]

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.18'

      - name: Setup Corepack
        run: |
          corepack enable
          corepack prepare yarn@4.5.3 --activate
          yarn --version # Verify correct version

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache yarn dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install Dependencies
        run: |
          yarn set version 4.5.3
          yarn install --immutable

      - name: Lint
        run: yarn lint apps/${{ matrix.service }}

      - name: Test
        run: yarn test apps/${{ matrix.service }}

      - name: Build
        run: yarn build apps/${{ matrix.service }}
