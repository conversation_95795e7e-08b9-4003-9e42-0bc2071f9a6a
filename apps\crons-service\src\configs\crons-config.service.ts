import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EnvironmentVariables } from './dto/env-variables.dto';

@Injectable()
export class CronsConfigService {
  constructor(private configService: ConfigService<EnvironmentVariables>) {}

  get port(): number {
    return this.configService.getOrThrow('CRONS_SERVICE_PORT');
  }

  get environment(): string {
    return this.configService.getOrThrow('NODE_ENV');
  }

  get coreServiceApiUrl(): number {
    return this.configService.getOrThrow('CORE_SERVICE_API_URL');
  }
}
