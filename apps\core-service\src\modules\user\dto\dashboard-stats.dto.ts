import { AIRecommendationsDto } from '@core-service/modules/portfolio/dto/ai-recommendation.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { MonthlyStatsDto } from '@core-service/modules/portfolio/dto/views.dto';
import { JobRecommendation } from '@core-service/modules/job/entities/job-match.entity';

export class PortfolioStrengthDto {
  @ApiProperty()
  about: number;

  @ApiProperty()
  professionalExperience: number;

  @ApiProperty()
  moduleShowcase: number;

  @ApiProperty()
  finalYearProject: number;

  @ApiProperty()
  researchAndInnovation: number;

  @ApiProperty()
  industrialAttachments: number;

  @ApiProperty()
  awardsAndRecognition: number;

  @ApiProperty()
  recommendations: number;

  @ApiProperty()
  extracurricularActivities: number;

  @ApiProperty()
  totalScore: number;
}

export class MetricChangeDto {
  @ApiProperty()
  current: number;

  @ApiProperty()
  percentageChange: number;

  @ApiProperty()
  lastUpdated: Date;
}

export class CareerMetricsDto {
  @Type(() => Date)
  date: Date;

  @IsNumber()
  skillsAcquired: number;

  @IsNumber()
  profileViews: number;

  @IsNumber()
  jobMatches: number;
}

export class DashboardStatsDto {
  @ValidateNested()
  @Type(() => PortfolioStrengthDto)
  portfolioStrength: PortfolioStrengthDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CareerMetricsDto)
  careerMetrics: CareerMetricsDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MonthlyStatsDto)
  monthlyAnalytics: MonthlyStatsDto[];

  @IsOptional()
  profileAIRecommendation?: AIRecommendationsDto;

  @IsOptional()
  jobRecommendations?: JobRecommendation[];

  @IsOptional()
  @IsNumber()
  activeSupervisions?: number;
}
