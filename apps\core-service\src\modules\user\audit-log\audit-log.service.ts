import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExceptionHandler } from '@app/common/exceptions/exceptions.handler';
import { AuditLog } from './entities/audit-log.entity';
import { EAuditLogAction } from './enums/audit-log-action.enum';
import { User } from '../entities/user.entity';
import { createPaginatedResponse } from '@app/common/helpers/pagination.helper';

@Injectable()
export class AuditLogService {
  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly exceptionHandler: ExceptionHandler,
  ) {}

  async create(user: User, action: EAuditLogAction): Promise<AuditLog> {
    const auditLog = this.auditLogRepository.create({
      user,
      action,
    });
    return await this.auditLogRepository.save(auditLog);
  }

  async findAll(
    user: User,
    page: number,
    limit: number,
    action?: EAuditLogAction,
    startDate?: Date,
    endDate?: Date,
  ) {
    const queryBuilder = this.auditLogRepository
      .createQueryBuilder('AuditLog')
      .leftJoinAndSelect('AuditLog.user', 'user')
      .where('user.college = :college', { college: user.college.id });

    if (action) {
      queryBuilder.andWhere('AuditLog.action = :action', { action });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere(
        'AuditLog.createdAt BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }
    const [logs, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return createPaginatedResponse(logs, total, page, limit);
  }
}
