import { Module, forwardRef } from '@nestjs/common';
import { User } from './entities/user.entity';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MinioClientModule } from '../minio-client/minio-client.module';
import { BrainModule } from '@app/common/brain/brain.module';
import { CollegeModule } from '../college/college.module';
import { PortfolioModule } from '../portfolio/portfolio.module';
import { Portfolio } from '../portfolio/entities/portfolio.entity';
import { AcademicProject } from '../academic-project/entities/academic-project.entity';
import { Project } from '../project/entities/project.entity';
import { JobModule } from '../job/job.module';
import { AuditLog } from './audit-log/entities/audit-log.entity';
import { AuditLogService } from './audit-log/audit-log.service';
import { AuditLogController } from './audit-log/audit-log.controller';
import { PublicController } from './public.controller';
import { ProjectModule } from '../project/project.module';
import { AcademicProjectModule } from '../academic-project/academic-project.module';
import { PublicService } from './public.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Portfolio,
      AuditLog,
      AcademicProject,
      Project,
    ]),
    BrainModule,
    MinioClientModule,
    CollegeModule,
    ProjectModule,
    PortfolioModule,
    AcademicProjectModule,
    forwardRef(() => JobModule),
  ],
  controllers: [UserController, AuditLogController, PublicController],
  providers: [UserService, AuditLogService, PublicService],
  exports: [UserService, PublicService],
})
export class UserModule {}
