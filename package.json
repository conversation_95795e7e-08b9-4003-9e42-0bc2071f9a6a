{"name": "cadastre-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "workspaces": ["apps/*", "libs/*"], "scripts": {"collect-protos": "sh scripts/copy-protos.sh", "prebuild": "yarn collect-protos", "predev": "yarn collect-protos", "build": "  yarn prebuild && nest build", "build:core": "yarn prebuild && nest build core-service", "build:integration": "yarn prebuild && nest build integration-service", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "yarn prebuild && nest start", "start:dev": " yarn predev && nest start --watch", "start:predev": "yarn predev && nest start", "start:debug": " yarn predev && nest start --debug --watch", "start:prod": "  yarn prebuild && node dist/apps/eportfolio-backend/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "yarn prebuild &&  jest", "test:watch": "j yarn prebuild && est --watch", "test:cov": "yarn prebuild &&  jest --coverage", "test:debug": "yarn prebuild &&  node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "yarn prebuild &&  jest --config ./apps/eportfolio-backend/test/jest-e2e.json"}, "dependencies": {"@faker-js/faker": "^9.6.0", "@grpc/grpc-js": "^1.12.6", "@grpc/proto-loader": "^0.7.13", "@grpc/reflection": "^1.0.4", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/mapped-types": "^2.0.6", "@nestjs/microservices": "^10.4.15", "@nestjs/platform-express": "^10.4.15", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/swagger": "^8.1.0", "@nestjs/typeorm": "^10.0.2", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.5", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "crypto-js": "^4.2.0", "diff-match-patch": "^1.0.5", "helmet": "^8.0.0", "ioredis": "^5.4.2", "lodash": "^4.17.21", "minio": "^8.0.3", "moment": "^2.30.1", "nestjs-minio-client": "^2.2.0", "nestjs-pino": "^4.2.0", "pg": "^8.13.1", "pino-http": "^10.3.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "validator": "^13.12.0", "yjs": "^13.6.21"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@types/bcryptjs": "^2", "@types/crypto-js": "^4", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4", "@types/minio": "^7.1.1", "@types/multer": "^1.4.12", "@types/mysql": "^2.15.26", "@types/node": "^20.17.12", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.2", "@types/validator": "^13", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.4.2", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/libs/", "<rootDir>/apps/"], "moduleNameMapper": {"^@app/common(|/.*)$": "<rootDir>/libs/common/src/$1", "^@core-service(|/.*)$": "<rootDir>/apps/core-service/src/$1", "^@integration-service(|/.*)$": "<rootDir>/apps/integration-service/src/$1", "^@notification-service(|/.*)$": "<rootDir>/apps/notification-service/src/$1", "^@hocus-pocus-service(|/.*)$": "<rootDir>/apps/hocus-pocus-service/src/$1", "^@crons-service(|/.*)$": "<rootDir>/apps/crons-service/src/$1"}}, "engines": {"node": "^20.18.0"}, "packageManager": "yarn@4.5.3"}