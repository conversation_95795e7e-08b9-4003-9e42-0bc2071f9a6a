{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "compilerOptions": {"deleteOutDir": true, "webpack": false, "tsConfigPath": "tsconfig.json", "watchAssets": true, "assets": ["assets/**/*"]}, "projects": {"common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "core-service": {"type": "application", "root": "apps/core-service", "entryFile": "main", "sourceRoot": "apps/core-service/src", "compilerOptions": {"tsConfigPath": "apps/core-service/tsconfig.app.json", "assets": [{"include": "**/*.proto", "watchAssets": true, "outDir": "dist/proto"}]}}, "notification-service": {"type": "application", "root": "apps/notification-service", "entryFile": "main", "sourceRoot": "apps/notification-service/src", "compilerOptions": {"tsConfigPath": "apps/notification-service/tsconfig.app.json", "assets": [{"include": "modules/email-notifier/templates/**/*", "watchAssets": true, "outDir": "dist/src"}]}}, "integration-service": {"type": "application", "root": "apps/integration-service", "entryFile": "main", "sourceRoot": "apps/integration-service/src", "compilerOptions": {"tsConfigPath": "apps/integration-service/tsconfig.app.json"}}, "hocus-pocus-service": {"type": "application", "root": "apps/hocus-pocus-service", "entryFile": "main", "sourceRoot": "apps/hocus-pocus-service/src", "compilerOptions": {"tsConfigPath": "apps/hocus-pocus-service/tsconfig.app.json"}}, "crons-service": {"type": "application", "root": "apps/crons-service", "entryFile": "main", "sourceRoot": "apps/crons-service/src", "compilerOptions": {"tsConfigPath": "apps/crons-service/tsconfig.app.json"}}}, "monorepo": true}