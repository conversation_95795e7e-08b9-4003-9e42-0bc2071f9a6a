import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateSettingsDto {
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  firstName: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  lastName: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  email: string;

  @ApiProperty({
    description: 'Profile Picture ',
    type: 'string',
    format: 'binary',
    required: false,
  })
  @IsOptional()
  picture: Express.Multer.File;
}
