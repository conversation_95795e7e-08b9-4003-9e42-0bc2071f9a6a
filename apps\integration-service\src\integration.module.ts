import { Module } from '@nestjs/common';
import { IntegrationConfigModule } from './configs/integration-config.module';
import { IntegrationConfigService } from './configs/integration-config.service';
import { ExceptionModule } from '@app/common/exceptions/exceptions.module';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from '@app/common/filters/exception.filters';
import { LoggerModule } from '@app/common/logger/logger.module';
import { HealthModule } from '@app/common/health/health.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LOG_BOOK_ROOT_NAME } from './common/constants/db.constants';

@Module({
  imports: [
    IntegrationConfigModule,
    TypeOrmModule.forRootAsync({
      imports: [IntegrationConfigModule],
      inject: [IntegrationConfigService],
      useFactory: async (appConfigService: IntegrationConfigService) =>
        appConfigService.getMysqlInfo(),
    }),
    TypeOrmModule.forRootAsync({
      imports: [IntegrationConfigModule],
      inject: [IntegrationConfigService],
      name: LOG_BOOK_ROOT_NAME,
      useFactory: async (appConfigService: IntegrationConfigService) =>
        appConfigService.getLogbookInfo(),
    }),
    LoggerModule,
    HealthModule,
    ExceptionModule,
  ],
  providers: [{ provide: APP_FILTER, useClass: AllExceptionsFilter }],
})
export class IntegrationServiceModule {}
