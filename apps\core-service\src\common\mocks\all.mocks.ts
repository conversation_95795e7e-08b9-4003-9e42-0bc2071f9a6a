export const MINIO_PROPERTIES_MOCK = {
  minioEndPoint: 'localhost',
  minioPort: '9000',
  minioUsessl: 'false',
  minioAccessKey: 'test-access-key',
  minioSecretKey: 'test-secret-key',
  minioBucket: 'test-bucket',
};

export const REDIS_CONFIG_MOCK = {
  get: jest.fn(),
  set: jest.fn(),
};
export const CLIENT_GRPC_MOCK = {
  getService: jest.fn().mockReturnValue({}), // Mock the gRPC service
};

export const CONFIG_SERVICE_MOCK = {
  minioEndPoint: 'localhost',
  minioPort: '9000',
  minioUsessl: 'false',
  minioAccessKey: 'test-access-key',
  minioSecretKey: 'test-secret-key',
  minioBucket: 'test-bucket',
};
