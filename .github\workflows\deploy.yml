name: Deploy to Production

on:
  push:
    branches: [post-launch]

jobs:
  deploy:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v3

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add SSH Known Hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to Server
        env:
          SERVER_HOST: ${{ secrets.SERVER_HOST }}
          SERVER_USER: ${{ secrets.SERVER_USER }}
          DEPLOY_PATH: ${{ secrets.DEPLOY_PATH }}
        run: |
          ssh $SERVER_USER@$SERVER_HOST << EOF
            # Exit on any error
            set -e

            # Error handling
            trap 'echo "Error on line \$LINENO"' ERR


            # Source profile and NVM
            export NVM_DIR="\$HOME/.nvm"
            [ -s "\$NVM_DIR/nvm.sh" ] && \. "\$NVM_DIR/nvm.sh"

            cd ${DEPLOY_PATH}

            git checkout post-launch
            git fetch origin post-launch
            git reset --hard origin/post-launch

            # Install dependencies
            yarn install --immutable

            # Build the applications
            yarn build core-service
            yarn build notification-service
            yarn build integration-service

            # Docker operations
            docker compose down
            docker compose build --no-cache
            docker compose up -d

            # Cleanup
            docker system prune -f
          EOF
