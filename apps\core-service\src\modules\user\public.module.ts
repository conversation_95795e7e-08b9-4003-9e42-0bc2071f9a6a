import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PublicController } from './public.controller';
import { PublicService } from './public.service';
import { PortfolioModule } from '../portfolio/portfolio.module';
import { Project } from '../project/entities/project.entity';
import { Portfolio } from '../portfolio/entities/portfolio.entity';
import { JobModule } from '../job/job.module';
import { ProjectModule } from '../project/project.module';
import { AcademicProjectModule } from '../academic-project/academic-project.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Project, Portfolio]),
    JobModule,
    PortfolioModule,
    ProjectModule,
    AcademicProjectModule,
  ],
  controllers: [PublicController],
  providers: [PublicService],
  exports: [PublicService],
})
export class PublicModule {}
