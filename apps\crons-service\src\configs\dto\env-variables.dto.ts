import { Exclude, Expose } from 'class-transformer';
import { IsEnum, IsNumber, IsString } from 'class-validator';

export enum AppEnvironment {
  Development = 'development',
  Production = 'production',
  Staging = 'staging',
  Test = 'test',
}

@Exclude()
export class EnvironmentVariables {
  @Expose()
  @IsEnum(AppEnvironment)
  NODE_ENV: AppEnvironment;

  @Expose()
  @IsNumber()
  CRONS_SERVICE_PORT: number;

  @Expose()
  @IsString()
  CORE_SERVICE_API_URL: string;

  @Expose()
  @IsString()
  AI_SERVICE_API_URL: string;
}
