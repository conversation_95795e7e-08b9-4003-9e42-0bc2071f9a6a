# node config
NODE_ENV=development

# central service config
CORE_SERVICE_PORT=4010

# pg config
DB_HOST='localhost'
DB_PORT=35000
DB_USER='user'
DB_PASS='password'
DB_NAME='cadastre'
DB_SYNCHRONIZE=true

# JWT config
JWT_SECRET='secret'
JWT_EXPIRES_IN='1d'



#Registration
ADMIN_REG_CODE=Ijabo@12323232-232 #Unique code to validate admin creation request

# RABBITMQ config
RABBITMQ_URI=amqp://localhost:5672

# Integration Grpc config
INTEGRATION_SERVICE_GRPC_URL=localhost:50051

# gRPC config
GRPC_HOST='localhost'
GRPC_PORT=50055

# notifications service config
RABBIT_MQ_NOTIFICATIONS_QUEUE=notifications
NOTIFICATIONS_PORT=4020
#Minio configuration

# MinIO config
 MINIO_ENDPOINT= 'localhost'
 MINIO_PORT= 9001
 MINIO_ACCESS_KEY= ''
 MINIO_SECRET_KEY= ''
 MINIO_BUCKET= 'cadstre'
 MINIO_USE_SSL=false

 #Frontend Urls
CLIENT_URL=https://cadastre.com/

# Encryption and AI Service config
ENCRYPTION_KEY=your_strong_encryption_key_here
AI_SERVICE_URL=http://localhost:8000
