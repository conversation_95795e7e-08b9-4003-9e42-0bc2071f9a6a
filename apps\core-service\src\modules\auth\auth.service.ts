import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { UserService } from '../user/user.service';
import { LoginDTO, LoginResDto } from './dto/login.dto';
import * as bcrypt from 'bcryptjs';
import { ExceptionHandler } from '@app/common/exceptions/exceptions.handler';
import { _400, _401, _409 } from '@app/common/constants/errors-constants';
import { EUserStatus } from '../user/enums/user-status.enum';
import { JwtService } from '@nestjs/jwt';
import { User } from '../user/entities/user.entity';
import { EmailTemplates } from '@core-service/configs/email-template-configs/email-templates.config';
import { NotificationPreProcessor } from '@core-service/integrations/notification/notification.preprocessor';
import { CoreServiceConfigService } from '@core-service/configs/core-service-config.service';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ActivateAccount } from './dto/activate-account.dto';
import { BrainService } from '@app/common/brain/brain.service';
import { plainToClass } from 'class-transformer';
import {
  FAILED_LOGIN_ATTEMPT,
  RESET_PASSWORD_CACHE,
} from '@core-service/common/constants/brain.constants';
import {
  FAILED_LOGIN_ATTEMPTS_TO_SEND_NOTIFICATION,
  MAX_FAILED_ATTEMPTS,
} from '@core-service/common/constants/all.constants';
import { hashPassword } from '@core-service/common/helpers/all.helpers';
import { ENotificationMessageType } from '@app/common/enums/notification-message-type.enum';
import { EUserRole } from '../user/enums/user-role.enum';

@Injectable()
export class AuthService {
  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly config: CoreServiceConfigService,
    private readonly jwt: JwtService,
    private readonly exceptionHandler: ExceptionHandler,
    private readonly notificationProcessor: NotificationPreProcessor,
    private readonly brainService: BrainService,
  ) {}

  async login(dto: LoginDTO): Promise<LoginResDto> {
    const userExists = await this.userService.existByEmail(dto.email);
    if (!userExists) {
      this.exceptionHandler.throwUnauthorized(_401.INVALID_CREDENTIALS);
    }

    let user: User = await this.userService.findByEmail(dto.email);
    if (user.status === EUserStatus.INACTIVE)
      this.exceptionHandler.throwBadRequest(_401.ACCOUNT_NOT_ACTIVE);

    const key = `${FAILED_LOGIN_ATTEMPT.name}:${user.email}`;
    const failedAttempts = await this.brainService.remindMe<number>(key);

    if (failedAttempts !== null && failedAttempts >= MAX_FAILED_ATTEMPTS) {
      this.exceptionHandler.throwUnauthorized(_401.ACCOUNT_LOCKED);
    }

    const passwordsMatch = await bcrypt.compare(
      dto.password.toString(),
      user.password.toString(),
    );

    if (!passwordsMatch) {
      if (failedAttempts >= FAILED_LOGIN_ATTEMPTS_TO_SEND_NOTIFICATION) {
        await Promise.all([
          await this.notificationProcessor.sendTemplateEmail(
            EmailTemplates.FAILED_LOGIN_ATTEMPT,
            [user.email],
            {
              userName: user.firstName,
            },
          ),
          await this.notificationProcessor.sendPlatformNotification({
            messageType: ENotificationMessageType.FAILED_LOGIN_ATTEMPT,
            recipients: [{ userId: user.id }],
            subject: `Failed login attempt`,
            metadata: {
              content: {
                title: 'Failed Login Attempt',
                description: 'An unsuccessful login attempt was detected',
                body: "If this wasn't you, we recommend updating your password.",
              },
            },
          }),
        ]);
      }
      await this.handleFailedLogin(key, failedAttempts);
      throw this.exceptionHandler.throwUnauthorized(_401.INVALID_CREDENTIALS);
    }
    await Promise.all([
      await this.notificationProcessor.sendTemplateEmail(
        EmailTemplates.NEW_LOGIN,
        [user.email],
        //
        {
          userName: `${user.firstName} ${user.lastName}`,
        },
      ),
      await this.notificationProcessor.sendPlatformNotification({
        messageType: ENotificationMessageType.NEW_LOGIN,
        recipients: [{ userId: user.id }],
        subject: `New login detected`,
        metadata: {
          content: {
            title: 'New device login detected',
            description:
              'We noticed you logged into ijabo from a new device or browser',
            body: "If this wasn't you, we recommend updating your password.",
          },
        },
      }),
    ]);
    user.lastLogin = new Date();
    const [token, savedUser] = await Promise.all([
      this.getToken(user),
      this.userService.saveUser(user),
    ]);
    user = plainToClass(User, savedUser);
    return { token, user };
  }
  async verifyOtp(id: string, otp: number) {
    const isOtpValid = await this.verifyOTP(id, otp);
    if (!isOtpValid) this.exceptionHandler.throwBadRequest(_400.INVALID_OTP);
  }
  async verifyAccount(dto: ActivateAccount): Promise<User> {
    const account: User = await this.userService.findByEmail(dto.email);
    await this.verifyOtp(account.id, dto.otp);
    account.status = EUserStatus.ACTIVE;
    return await this.userService.saveUser(account);
  }
  async sendOpt(
    email: string,
    onboarding?: boolean,
    registrationNumber?: string,
    role?: EUserRole,
  ) {
    if (onboarding) {
      const existsByEmail = await this.userService.existByEmail(email);
      if (existsByEmail) {
        this.exceptionHandler.throwConflict(_409.USER_ALREADY_ONBOARDED);
      }
      const [otp] = await Promise.all([
        this.brainService.generateOTP(registrationNumber),
      ]);
      return await this.userService.cacheUser(
        registrationNumber,
        email,
        role,
        otp,
      );
    }

    const account: User = await this.userService.findByEmail(email);
    const otp = await this.brainService.generateOTP(account.id);
    await this.notificationProcessor.sendTemplateEmail(
      EmailTemplates.VERIFICATION,
      [account.email],
      {
        userName: account.firstName,
        verificationUrl: `${this.config.clientUrl}/auth/reset-password/?email=${account.email}&verification_code=${otp}`,
      },
    );
  }

  async resetPassword(dto: ResetPasswordDto): Promise<User> {
    const user = await this.userService.findByEmail(dto.email);
    if (user.status === EUserStatus.INACTIVE) {
      this.exceptionHandler.throwBadRequest(_401.ACCOUNT_NOT_VERIFIED);
    }
    await this.verifyOtp(user.id, dto.otp);
    user.password = await hashPassword(dto.newPassword.toString());
    const updatedUser: User = await this.userService.saveUser(user);
    await Promise.all([
      await this.notificationProcessor.sendTemplateEmail(
        EmailTemplates.PASSWORD_RESET,
        [user.email],
        {
          userName: user.firstName,
        },
      ),
      await this.notificationProcessor.sendPlatformNotification({
        messageType: ENotificationMessageType.PASSWORD_RESET,
        recipients: [{ userId: user.id }],
        subject: `Your password was changed`,
        metadata: {
          content: {
            title: 'Your Password Was Changed',
            description: 'Your password was successfully changed',
          },
        },
      }),
    ]);
    return plainToClass(User, updatedUser);
  }
  private async getToken(user: User): Promise<string> {
    return await this.jwt.signAsync(
      { role: user.role, id: user.id },
      {
        expiresIn: this.config.jwtExpiresIn,
        secret: this.config.jwtSecret,
      },
    );
  }

  private async verifyOTP(userId: string, otp: number): Promise<boolean> {
    const key = `${RESET_PASSWORD_CACHE.name}:${userId}`;
    const storedOTP = await this.brainService.remindMe<number>(key);

    if (!storedOTP || storedOTP !== otp) {
      return false;
    }
    await this.brainService.forget(key);
    return true;
  }

  private async handleFailedLogin(
    key: string,
    failedAttempts: number,
  ): Promise<void> {
    if (failedAttempts === null) {
      await this.brainService.memorize(key, 1, FAILED_LOGIN_ATTEMPT.ttl);
    } else {
      await this.brainService.memorize(
        key,
        failedAttempts + 1,
        FAILED_LOGIN_ATTEMPT.ttl,
      );
    }
  }

  async generateCustomToken<T extends object>(object: T): Promise<string> {
    return await this.jwt.signAsync(
      { ...object },
      {
        expiresIn: '10y',
        secret: this.config.jwtSecret,
      },
    );
  }
}
