import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Delete,
  Patch,
  Req,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { UserService } from './user.service';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiConsumes,
} from '@nestjs/swagger';
import { CreateUserDTO } from './dto/create-user.dto';
import { _errors } from '@app/common/helpers/shared.helpers';
import { _404, _409 } from '@app/common/constants/errors-constants';
import {
  AuthUser,
  PreAuthorize,
} from '@core-service/decorators/auth.decorator';
import { EUserRole } from './enums/user-role.enum';
import { Public } from '@app/common/decorators/public.decorator';
import { EUserStatus } from './enums/user-status.enum';
import { CreateAdminDTO } from './dto/create-admin.dto';
import { User } from './entities/user.entity';
import { OnboardUserDto } from './dto/onboard-user.dto';
import { ConfirmUserProfileDto } from './dto/confirm-profile.dto';
import { EUpdateAction } from './enums/update-action.enum';
import { UpdateTwoFactorAuthDto } from './dto/update-two-factor-auth.dto';
import { UpdateEmailNotificationsDto } from './dto/update-email-notifications.dto';
import { UpdateUserPasswordDto } from './dto/update-user-password.dto';
import { UpdateSettingsDto } from './dto/update-settings.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { normalizeArray } from '@core-service/common/helpers/all.helpers';
import { ESortDirection } from '@app/common/enums/sort-direction.enum';
import { EducationLevel } from './enums/education-level.enum';

@Controller('user')
@ApiTags('user')
@ApiBearerAuth()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('/onboard/')
  @Public()
  @ApiOperation({
    summary: 'use this api to onboard new user by email and code',
  })
  onboardUser(@Body() dto: OnboardUserDto) {
    return this.userService.onboardUser(dto);
  }

  @Patch('/onboard/confirm-profile')
  @Public()
  @ApiOperation({
    summary: 'Use this api to confirm user details',
  })
  async confirmUserProfile(@Body() confirmProfileDto: ConfirmUserProfileDto) {
    return await this.userService.confirmUserProfile(confirmProfileDto);
  }
  @Get('mis-info')
  @Public()
  @ApiQuery({ name: 'otp', type: 'number', required: true })
  @ApiQuery({ name: 'registrationNumber', type: 'string', required: true })
  @ApiOperation({
    summary: 'This API retrieves all MIS information regarding the user',
  })
  async getMisInfo(
    @Query('otp') otp: number,
    @Query('registrationNumber') registrationNumber: string,
  ) {
    return this.userService.getMisInfo(otp, registrationNumber);
  }
  @Post()
  @ApiBody({ type: CreateUserDTO })
  @ApiNotFoundResponse(_errors([_404.COMPANY_NOT_FOUND]))
  @ApiConflictResponse(_errors([_409.USER_ALREADY_EXISTS]))
  @ApiCreatedResponse({ type: [User] })
  @PreAuthorize(EUserRole.SUPER_ADMIN)
  createUser(@Body() createUserDto: CreateUserDTO) {
    return this.userService.create(createUserDto);
  }

  @Post('admin')
  @ApiConflictResponse(_errors([_409.USER_ALREADY_EXISTS]))
  @ApiCreatedResponse({ type: [User] })
  @ApiBody({ type: CreateAdminDTO })
  @Public()
  createAdmin(@Body() createUserDto: CreateAdminDTO) {
    return this.userService.createAdmin(createUserDto);
  }

  @Get()
  @ApiQuery({ name: 'page', required: false, example: 1 })
  @ApiQuery({ name: 'limit', required: false, example: 10 })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'statuses', isArray: true, type: String, required: false })
  @ApiQuery({
    name: 'roles',
    required: false,
  })
  @ApiQuery({ name: 'searchKey', required: false })
  @PreAuthorize(EUserRole.SUPER_ADMIN)
  @AuthUser()
  async getAll(
    @Query('statuses') statuses: EUserStatus[],
    @Query('searchKey') searchKey: string,
    @Query('roles') roles: EUserRole[],
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Req() req,
  ) {
    const parsedStatuses = Array.isArray(statuses)
      ? statuses
      : statuses
        ? [statuses]
        : [];
    const parsedRoles = Array.isArray(roles) ? roles : roles ? [roles] : [];
    const users = this.userService.findAll(
      parsedRoles,
      req.user as User,
      parsedStatuses,
      searchKey,
      page,
      limit,
    );
    return users;
  }

  @Get('all/by-college')
  @ApiQuery({ name: 'page', required: false, example: 1 })
  @ApiQuery({ name: 'limit', required: false, example: 10 })
  @ApiQuery({ name: 'sortColumn', required: false, type: String, example: 1 })
  @ApiQuery({ name: 'sortDirection', required: false, example: 1 })
  @ApiQuery({ name: 'statuses', isArray: true, type: String, required: false })
  @ApiQuery({
    name: 'departmentIds',
    isArray: true,
    type: String,
    required: false,
  })
  @ApiQuery({
    name: 'roles',
    type: String,
    isArray: true,
    required: false,
  })
  @ApiQuery({ name: 'searchKey', required: false })
  @AuthUser()
  async findAllByCollege(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('statuses') statuses: EUserStatus[],
    @Query('roles') roles: EUserRole[],
    @Query('departmentIds') departmentIds: string[],
    @Req() req,
    @Query('sortColumn') sortColumn: string,
    @Query('sortDirection') sortDirection: ESortDirection,
    @Query('searchKey') keyword?: string,
  ) {
    const parsedRoles = normalizeArray<EUserRole>(roles);
    const parsedStatuses = normalizeArray<EUserStatus>(statuses);
    const parsedDepartmentIds = normalizeArray<string>(departmentIds);
    return this.userService.findAllByCollege(
      parsedRoles,
      req.user as User,
      parsedStatuses,
      parsedDepartmentIds,
      keyword,
      sortColumn,
      sortDirection,
      page,
      limit,
    );
  }
  @Get('/mis-integrated/')
  @PreAuthorize(EUserRole.SUPER_ADMIN)
  @ApiQuery({ name: 'searchKeyword', required: false })
  async findAllMISUsers(@Query('searchKeyword') searchKeyword: string) {
    return this.userService.findAllMISUsers(searchKeyword);
  }

  @Get('/email/:email')
  @ApiNotFoundResponse(_errors([_404.USER_NOT_FOUND]))
  @ApiParam({ name: 'email', required: true })
  @PreAuthorize(EUserRole.SUPER_ADMIN)
  async getUserByEmail(@Param('email') email: string) {
    const user = await this.userService.findByEmail(email);
    return user;
  }

  @Get('by-id/:id')
  @ApiNotFoundResponse(_errors([_404.USER_NOT_FOUND]))
  @ApiQuery({ name: 'id', required: true })
  @PreAuthorize(EUserRole.SUPER_ADMIN)
  async getUserById(@Query('id') id: string) {
    const user = await this.userService.findOne(id);
    return user;
  }

  @Patch('two-factor-auth')
  @AuthUser()
  @ApiOperation({ summary: 'Toggle two-factor authentication status' })
  @ApiBody({ type: UpdateTwoFactorAuthDto })
  async toggleTwoFactorAuth(@Req() req, @Body() dto: UpdateTwoFactorAuthDto) {
    return this.userService.activateOrDeactivateTwoFactorAuth(
      req.user as User,
      dto.action,
    );
  }

  @Patch('email-notifications')
  @AuthUser()
  @ApiOperation({ summary: 'Toggle email notifications status' })
  @ApiBody({ type: UpdateEmailNotificationsDto })
  async toggleEmailNotifications(
    @Req() req,
    @Body() dto: UpdateEmailNotificationsDto,
  ) {
    return this.userService.toggleEmailNotifications(
      req.user as User,
      dto.action,
    );
  }

  @ApiParam({ name: 'id', required: true })
  @ApiNotFoundResponse(_errors([_404.USER_NOT_FOUND]))
  @ApiBody({ type: CreateUserDTO })
  @PreAuthorize(EUserRole.SUPER_ADMIN)
  @Patch(':id')
  updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: Partial<CreateUserDTO>,
  ) {
    return this.userService.update(id, updateUserDto);
  }

  @Patch('/activate/:id')
  @ApiParam({ name: 'id', required: true })
  @ApiNotFoundResponse(_errors([_404.USER_NOT_FOUND]))
  @PreAuthorize(EUserRole.SUPER_ADMIN, EUserRole.COLLEGE_ADMIN)
  activateUser(@Param('id') id: string) {
    return this.userService.activateOrDeactivateUser(
      id,
      EUpdateAction.ACTIVATE,
    );
  }
  @Patch('/deactivate/:id')
  @ApiParam({ name: 'id', required: true })
  @ApiNotFoundResponse(_errors([_404.USER_NOT_FOUND]))
  @PreAuthorize(EUserRole.SUPER_ADMIN, EUserRole.COLLEGE_ADMIN)
  deactivateUser(@Param('id') id: string) {
    return this.userService.activateOrDeactivateUser(
      id,
      EUpdateAction.DEACTIVATE,
    );
  }

  @Patch('settings/change-password')
  @AuthUser()
  async updatePassword(
    @Req() req,
    @Body() dto: UpdateUserPasswordDto,
  ): Promise<User> {
    return this.userService.updatePassword(dto, req.user as User);
  }

  @Patch('settings/change-info')
  @AuthUser()
  @UseInterceptors(FileInterceptor('picture'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: UpdateSettingsDto,
  })
  updateSettings(
    @Req() req,
    @UploadedFile() picture: Express.Multer.File,
    @Body() updateSettingsDto: UpdateSettingsDto,
  ) {
    return this.userService.updateUserInfo(
      req.user as User,
      updateSettingsDto,
      picture,
    );
  }

  @Delete('/:id')
  @ApiParam({ name: 'id', required: true })
  @PreAuthorize(EUserRole.SUPER_ADMIN)
  async deleteUser(@Param('id') id: string) {
    await this.userService.delete(id);
  }

  @Get('/get-students/')
  @ApiQuery({ name: 'academicCollaborators', required: false, default: false })
  @AuthUser()
  findAllStudents(
    @Req() req,
    @Query('academicCollaborators') academicCollaborators: boolean,
  ) {
    return this.userService.findAllStudents(
      req.user as User,
      academicCollaborators,
    );
  }

  @Get('/get-lectures/')
  @ApiQuery({ name: 'searchKeyword', required: false })
  @AuthUser()
  findAllLectures(@Query('searchKeyword') searchKeyword: string, @Req() req) {
    return this.userService.findAllLectures(searchKeyword, req.user as User);
  }

  @Get('/incubation-center-staff/')
  @ApiQuery({ name: 'searchKeyword', required: false })
  @AuthUser()
  findAllIncubationCenterStaff(
    @Query('searchKeyword') searchKeyword: string,
    @Req() req,
  ) {
    return this.userService.findAllIncubationCenterStaff(
      searchKeyword,
      req.user as User,
    );
  }

  @Get('/get-allumninae/')
  @ApiQuery({ name: 'searchKeyword', required: false })
  @AuthUser()
  findAllAluminae(@Query('searchKeyword') searchKeyword: string, @Req() req) {
    return this.userService.findAllAllumninae(
      searchKeyword,
      req.user as User,
      EducationLevel.ALUMNI,
    );
  }
  @Get('users-to-invite/filtered')
  @ApiQuery({ name: 'searchKeyword', required: false })
  @ApiQuery({ name: 'role', required: false })
  @ApiQuery({ name: 'educationLevel', required: false })
  @AuthUser()
  findAllUsersToInvite(
    @Query('searchKeyword') searchKeyword: string,
    @Query('role') role: EUserRole,
    @Query('educationLevel') educationLevel: EducationLevel,
    @Req() req,
  ) {
    return this.userService.findAllUsersToInvite(
      searchKeyword,
      req.user as User,
      educationLevel,
      role,
    );
  }
}
