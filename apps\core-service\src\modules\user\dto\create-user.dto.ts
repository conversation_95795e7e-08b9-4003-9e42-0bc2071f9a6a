import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { EUserRole } from '../enums/user-role.enum';
import { Type } from 'class-transformer';

export class DepartmentDto {
  @ApiProperty()
  @IsNotEmpty()
  departmentName: string;

  @ApiProperty()
  @IsNotEmpty()
  departmentType: string;

  @ApiProperty()
  @IsNotEmpty()
  departmentId: string;
}

export class CollegeDto {
  @ApiProperty()
  @IsNotEmpty()
  collegeName: string;

  @ApiProperty()
  @IsNotEmpty()
  collegeId: string;
}
export class CreateUserDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  lastName: string;

  @IsEmail()
  @IsNotEmpty()
  @ApiProperty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  phoneNumber: string;

  @IsNotEmpty()
  @ApiProperty()
  role: EUserRole;

  @IsOptional()
  @ApiProperty({ required: false })
  companyId?: string;

  @IsOptional()
  @ApiProperty({ required: false })
  collegeName: string;

  @IsOptional()
  @ApiProperty({ required: false })
  departmentName: string;
}
