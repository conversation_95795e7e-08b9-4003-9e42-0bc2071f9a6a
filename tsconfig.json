{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2021",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@app/common": [
        "libs/common/src"
      ],
      "@app/common/*": [
        "libs/common/src/*"
      ],
      "@core-service/": [
        "apps/core-service/src"
      ],
      "@core-service/*": [
        "apps/core-service/src/*"
      ],
      "@integration-service/": [
        "apps/integration-service/src"
      ],
      "@integration-service/*": [
        "apps/integration-service/src/*"
      ],
      "@notification-service/": [
        "apps/notification-service/src"
      ],
      "@notification-service/*": [
        "apps/notification-service/src/*"
      ],
      "@hocus-pocus-service/": [
        "apps/hocus-pocus-service/src"
      ],
      "@hocus-pocus-service/*": [
        "apps/hocus-pocus-service/src/*"
      ],
      "@crons-service/": [
        "apps/crons-service/src"
      ],
      "@crons-service/*": [
        "apps/crons-service/src/*"
      ]
    },
  },
  "exclude": ["node_modules"]
}
