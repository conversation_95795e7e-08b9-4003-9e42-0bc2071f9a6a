# PostgreSQL Configuration
POSTGRES_USER=cadastre
POSTGRES_PASSWORD=<strong-password>
POSTGRES_DB=ijabo_db

# Redis Configuration
REDIS_PASSWORD=<strong-password>
REDIS_PORT=6379
REDIS_DATABASES=1
REDIS_URL=redis://:<password>@localhost:6379/1

# PostgreSQL Notification Configuration
POSTGRES_NOTIFICATION_USER=postgres
POSTGRES_NOTIFICATION_PASSWORD=123
POSTGRES_NOTIFICATION_DB=ijabo_notification_db

# MySQL Configuration
MYSQL_DATABASE=mis
MYSQL_USER=ijabo
MYSQL_PASSWORD=<strong-password>
MYSQL_ROOT_PASSWORD=<strong-password>
MYSQL_ROOT_HOST=%

#Minio Database
MINIO_ROOT_USER=minio
MINIO_ROOT_PASSWORD=client%321
