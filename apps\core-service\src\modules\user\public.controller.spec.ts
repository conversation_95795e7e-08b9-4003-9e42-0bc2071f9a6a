import { Test, TestingModule } from '@nestjs/testing';
import { PublicController } from './public.controller';
import { PublicService } from './public.service';
import { ProjectService } from '../project/project.service';
import { PortfolioService } from '../portfolio/portfolio.service';
import { AcademicProjectService } from '../academic-project/academic-project.service';
import { JobService } from '../job/job.service';
import { EUserRole } from './enums/user-role.enum';
import { EProjectStatus } from '../project/enums/project-status.enum';
import { EProjectType } from '../project/enums/project-type.enum';
import { EducationLevel } from './enums/education-level.enum';

describe('PublicController', () => {
  let controller: PublicController;
  let publicService: PublicService;
  let projectService: ProjectService;
  let portfolioService: PortfolioService;
  let academicProjectService: AcademicProjectService;
  let jobService: JobService;

  const mockPublicService = {
    getLandingStats: jest.fn(),
    countProjectView: jest.fn(),
    countPortfolioView: jest.fn(),
  };

  const mockProjectService = {
    findOne: jest.fn(),
    getAllPublishedProjects: jest.fn(),
    findProjectForPublic: jest.fn(),
  };

  const mockPortfolioService = {
    findAllProfiles: jest.fn(),
  };

  const mockAcademicProjectService = {
    findProjectForPublic: jest.fn(),
  };

  const mockJobService = {
    findAllJobsForPublic: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PublicController],
      providers: [
        {
          provide: PublicService,
          useValue: mockPublicService,
        },
        {
          provide: ProjectService,
          useValue: mockProjectService,
        },
        {
          provide: PortfolioService,
          useValue: mockPortfolioService,
        },
        {
          provide: AcademicProjectService,
          useValue: mockAcademicProjectService,
        },
        {
          provide: JobService,
          useValue: mockJobService,
        },
      ],
    }).compile();

    controller = module.get<PublicController>(PublicController);
    publicService = module.get<PublicService>(PublicService);
    projectService = module.get<ProjectService>(ProjectService);
    portfolioService = module.get<PortfolioService>(PortfolioService);
    academicProjectService = module.get<AcademicProjectService>(
      AcademicProjectService,
    );
    jobService = module.get<JobService>(JobService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getFeaturedContent', () => {
    it('should return landing stats', async () => {
      const mockStats = {
        topProjects: [],
        topTeacherPortfolios: [],
        topStudentPortfolios: [],
        projectsCount: 10,
        innovationnsCount: 5,
        portfolioCount: 15,
      };

      mockPublicService.getLandingStats.mockResolvedValue(mockStats);

      const result = await controller.getFeaturedContent();

      expect(result).toEqual(mockStats);
      expect(mockPublicService.getLandingStats).toHaveBeenCalled();
    });
  });

  describe('viewProject', () => {
    it('should return project details', async () => {
      const mockProject = {
        id: '1',
        title: 'Test Project',
        status: EProjectStatus.PUBLISHED,
      };

      mockProjectService.findProjectForPublic.mockResolvedValue(mockProject);

      const result = await controller.viewProject('1');

      expect(result).toEqual(mockProject);
      expect(mockProjectService.findProjectForPublic).toHaveBeenCalledWith('1');
    });
  });

  describe('getAllPublishedProjects', () => {
    it('should return paginated projects', async () => {
      const mockProjects = {
        items: [],
        total: 10,
        page: 1,
        limit: 10,
      };

      mockProjectService.getAllPublishedProjects.mockResolvedValue(
        mockProjects,
      );

      const result = await controller.getAllPublishedProjects(
        'search',
        EProjectType.RESEARCH,
        EUserRole.STUDENT,
        EducationLevel.LEVEL_SIX,
        'college1',
        'dept1',
        1,
        10,
      );

      expect(result).toEqual(mockProjects);
      expect(mockProjectService.getAllPublishedProjects).toHaveBeenCalledWith(
        'search',
        [EProjectType.RESEARCH],
        EUserRole.STUDENT,
        [EducationLevel.LEVEL_SIX],
        ['dept1'],
        ['college1'],
        1,
        10,
      );
    });
  });

  describe('findAllProfiles', () => {
    it('should return paginated profiles', async () => {
      const mockProfiles = {
        items: [],
        total: 10,
        page: 1,
        limit: 10,
      };

      mockPortfolioService.findAllProfiles.mockResolvedValue(mockProfiles);

      const result = await controller.findAllProfiles(
        EducationLevel.LEVEL_SIX,
        'search',
        'dept1',
        'college1',
        'category',
        ['skill1', 'skill2'],
        1,
        10,
      );

      expect(result).toEqual(mockProfiles);
      expect(mockPortfolioService.findAllProfiles).toHaveBeenCalledWith(
        1,
        10,
        'category',
        EducationLevel.LEVEL_SIX,
        'search',
        'dept1',
        'college1',
      );
    });
  });
  describe('viewProject', () => {
    it('should return academic project if found', async () => {
      const mockAcademicProject = {
        id: '1',
        title: 'Academic Project',
      };

      mockAcademicProjectService.findProjectForPublic.mockResolvedValue(
        mockAcademicProject,
      );
      mockProjectService.findProjectForPublic.mockResolvedValue(null);

      const result = await controller.viewProject('1');

      expect(result).toEqual(mockAcademicProject);
    });

    it('should return research/innovation project if academic project not found', async () => {
      const mockProject = {
        id: '1',
        title: 'Research Project',
      };

      mockAcademicProjectService.findProjectForPublic.mockResolvedValue(null);
      mockProjectService.findProjectForPublic.mockResolvedValue(mockProject);

      const result = await controller.viewProject('1');

      expect(result).toEqual(mockProject);
    });

    it('should return null if no project found', async () => {
      mockAcademicProjectService.findProjectForPublic.mockResolvedValue(null);
      mockProjectService.findProjectForPublic.mockResolvedValue(null);

      const result = await controller.viewProject('1');

      expect(result).toBeNull();
    });
  });
});
