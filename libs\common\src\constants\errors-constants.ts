// not found
export const _400 = {
  EMPTY_EXCEL_FILE: {
    code: 'EMPTY_EXCEL_FILE',
    message: 'The excel file is empty',
  },
};

// unauthorized
export const _401 = {
  ACCOUNT_NOT_VERIFIED: {
    code: 'ACCOUNT_NOT_VERIFIED',
    message: 'The account is not yet verified',
  },
  ACCOUNT_NOT_ACTIVE: {
    code: 'ACCOUNT_NOT_ACTIVE',
    message: 'The account is not active',
  },
  INVALID_CREDENTIALS: {
    code: 'INVALID_CREDENTIALS',
    message: 'Invalid credentials provided',
  },
  AUTH_INVALID_TOKEN: {
    code: 'AUTH_INVALID_TOKEN',
    message: 'Invalid JWT Token',
  },
  AUTH_TOKEN_EXPIRED: {
    code: 'AUTH_TOKEN_EXPIRED',
    message: 'JWT Token Expired',
  },
  MALFORMED_TOKEN: {
    code: 'MALFORMED_TOKEN',
    message: 'The provided token is malformed.',
  },
  TOKEN_EXPIRED: {
    code: 'TOKEN_EXPIRED',
    message: 'The provided token was expired.',
  },
  ACCOUNT_LOCKED: {
    code: 'ACCOUNT_LOCKED',
    message: 'This account has been temporarily locked',
  },
};

// forbidden
export const _403 = {
  UNAUTHORIZED: {
    code: 'UNAUTHORIZED',
    message: 'You are not authorized to perform this action',
  },
};

// not found
export const _404 = {
  USER_NOT_FOUND: {
    code: 'USER_NOT_FOUND',
    message: 'No User exists with provided id',
  },
  DATABASE_RECORD_NOT_FOUND: {
    code: 'DATABASE_RECORD_NOT_FOUND',
    message: 'The record does not exist',
  },
};

// conflict
export const _409 = {
  DATABASE_RECORD_ALREADY_EXISTS: {
    code: 'DATABASE_RECORD_ALREADY_EXISTS',
    message: 'The record already exists',
  },
  FOREIGN_KEY_VIOLATION: {
    code: 'FOREIGN_KEY_VIOLATION',
    message: 'The record is referenced by another record',
  },
};

// internal server error
export const _500 = {
  INTERNAL_SERVER_ERROR: {
    code: 'INTERNAL_SERVER_ERROR',
    message: 'The service is temporarily not available',
  },
};

// service unavailable
export const _503 = {
  EXTERNAL_SERVICE_UNAVAILABLE: {
    code: 'EXTERNAL_SERVICE_UNAVAILABLE',
    message: 'External service is temporarily unavailable',
  },
};

type ValueOf<T> = T[keyof T];

export type TypeOfError =
  | ValueOf<typeof _400>
  | ValueOf<typeof _401>
  | ValueOf<typeof _403>
  | ValueOf<typeof _404>
  | ValueOf<typeof _409>
  | ValueOf<typeof _500>
  | ValueOf<typeof _503>;
