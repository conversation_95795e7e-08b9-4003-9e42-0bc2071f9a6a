import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Repository } from 'typeorm';
import { CreateUserDTO } from './dto/create-user.dto';
import { ClientGrpc } from '@nestjs/microservices';
import * as bcrypt from 'bcryptjs';
import { ExceptionHandler } from '@app/common/exceptions/exceptions.handler';
import { _400, _404, _409 } from '@app/common/constants/errors-constants';
import { INTEGRATION_GRPC_PACKAGE } from '@app/common/constants/services-constants';
import { StudentsService } from '@integration-service/modules/student/students.service';
import { GrpcServices } from '@integration-service/common/constants/grpc.constants';
import { NotificationPreProcessor } from '@core-service/integrations/notification/notification.preprocessor';
import { EUserStatus } from './enums/user-status.enum';
import { EUserRole } from './enums/user-role.enum';
import { CoreServiceConfigService } from '@core-service/configs/core-service-config.service';
import { CreateAdminDTO } from './dto/create-admin.dto';
import { createPaginatedResponse } from '@app/common/helpers/pagination.helper';
import { plainToClass } from 'class-transformer';
import { PlatformQueuePayload } from '@app/common/interfaces/shared-queues/platform-queue-payload.interface';
import { USER_BY_ID_CACHE } from '@core-service/common/constants/brain.constants';
import { BrainService } from '@app/common/brain/brain.service';
import { MinioClientService } from '../minio-client/minio-client.service';
import { EmailTemplates } from '@core-service/configs/email-template-configs/email-templates.config';
import { lastValueFrom, Observable } from 'rxjs';
import { OnboardUserDto } from './dto/onboard-user.dto';
import { ConfirmUserProfileDto } from './dto/confirm-profile.dto';
import {
  generateAlphaNumericCode,
  hashPassword,
} from '@core-service/common/helpers/all.helpers';
import { PortfolioService } from '../portfolio/portfolio.service';
import { UpdateSettingsDto } from './dto/update-settings.dto';
import { CollegeService } from '../college/college.service';
import { TeacherService } from '@integration-service/modules/teacher/teachers.service';
import { EGender } from '@app/common/enums';
import { ENotificationMessageType } from '@app/common/enums/notification-message-type.enum';
import { DepartmentService } from '../portfolio/department.service';
import { Department } from '../portfolio/entities/department.entity';
import { UserIntegrationResponseDto } from '@integration-service/common/dto/user-integration-response.dto';
import * as moment from 'moment';
import { DepartmentIntegration } from '@integration-service/modules/department/entitites/department.entity';
import { EUpdateAction } from './enums/update-action.enum';
import { UpdateUserPasswordDto } from './dto/update-user-password.dto';
import { College } from '../college/entities/college.entity';
import { ESortDirection } from '@app/common/enums/sort-direction.enum';
import { Portfolio } from '../portfolio/entities/portfolio.entity';
import { CareerMetricsDto } from './dto/dashboard-stats.dto';
import { Project } from '../project/entities/project.entity';
import { ETimeFrequency } from './enums/time-period.enum';
import { ECollaborStatus } from '../project/enums/collaborator-status.enum';
import { UserIntegrationService } from '@integration-service/modules/user/user.service';
import { JobService } from '../job/job.service';
import { AuditLogService } from './audit-log/audit-log.service';
import { EAuditLogAction } from './audit-log/enums/audit-log-action.enum';
import { ViewsDto } from '../portfolio/dto/view.dto';
import { EViewType } from '../portfolio/enums/view-type.enum';
import { EProjectVisibility } from '../project/enums/project-visibility.enum';
import { EportfolioStatus } from '../portfolio/enums/portfolio-status.enum';
import { extractTopSkills } from '../portfolio/helpers/all.helper';
import { EProjectType } from '../project/enums/project-type.enum';
import { IndustrialAttachmentService } from '@integration-service/modules/industrial-attachment/industrial-attachment.service';
import { IndustrialAttachmentDTO } from '../portfolio/dto/industrial-attachment.dto';
import { AttachmentResponnse } from '@integration-service/modules/industrial-attachment/dto/attachment-lookup.dto';
import { EducationLevel } from './enums/education-level.enum';
import { CollegeIntegration } from '@integration-service/modules/college/entities/college.entity';
import { ECollaborationRole } from '../project/enums/collaboration-role.enum';
import { compact } from 'lodash';
import { AcademicProject } from '../academic-project/entities/academic-project.entity';
import { ProjectCollaborator } from '../project/entities/project-collaborator.entity';
import { EProjectStatus } from '../project/enums/project-status.enum';

@Injectable()
export class UserService {
  private readonly integrationStudentService: StudentsService;
  private readonly integrationTeacherService: TeacherService;
  private readonly userIntegrationService: UserIntegrationService;
  private readonly attachmentsIntegrationService: IndustrialAttachmentService;

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(AcademicProject)
    private readonly academicProjectRepository: Repository<AcademicProject>,
    @InjectRepository(Portfolio)
    private readonly portfolioRepository: Repository<Portfolio>,
    private readonly notificationProcessor: NotificationPreProcessor,
    private readonly configService: CoreServiceConfigService,
    private readonly collegeService: CollegeService,
    private readonly exceptionHandler: ExceptionHandler,
    @Inject(INTEGRATION_GRPC_PACKAGE) private readonly client: ClientGrpc,
    private readonly brainService: BrainService,
    private readonly minioService: MinioClientService,
    private readonly portfolioService: PortfolioService,
    private readonly departmentService: DepartmentService,
    private readonly config: CoreServiceConfigService,
    private readonly jobService: JobService,
    private readonly auditLogService: AuditLogService,
  ) {
    this.integrationStudentService = this.client.getService<StudentsService>(
      GrpcServices.STUDENT_SERVICE,
    );
    this.integrationTeacherService = this.client.getService<TeacherService>(
      GrpcServices.LECTURE_SERVICE,
    );

    this.attachmentsIntegrationService =
      this.client.getService<IndustrialAttachmentService>(
        GrpcServices.INDUSTRIAL_ATTACHMENT_SERVICE,
      );

    this.userIntegrationService =
      this.client.getService<UserIntegrationService>(
        GrpcServices.USER_INTEGRATION_SERVICE,
      );
  }

  async onboardUser(dto: OnboardUserDto) {
    const userExists = await this.existByEmail(dto.email);
    if (userExists) {
      this.exceptionHandler.throwBadRequest(_409.USER_ALREADY_ONBOARDED);
    }
    switch (dto.role) {
      case EUserRole.STUDENT:
        return await this.onboardStudentByRegNumber(
          dto.registrationNumber,
          dto.email,
          EUserRole.STUDENT,
        );
      case EUserRole.LECTURE:
        return await this.onboardTeacherByCodeAndEmail(dto, false);
      case EUserRole.ALUMNI:
        return await this.onboardStudentByRegNumber(
          dto.registrationNumber,
          dto.email,
          EUserRole.ALUMNI,
        );
      case EUserRole.HoD:
        return await this.onboardTeacherByCodeAndEmail(dto, true);
      case EUserRole.INCUBATOR:
        break;
      default:
        this.exceptionHandler.throwBadRequest(_400.INVALID_USER_ROLE);
    }
  }

  async onboardStudentByRegNumber(
    regNumber: string,
    email: string,
    role: EUserRole,
  ) {
    try {
      const otp = await this.brainService.generateOTP(regNumber);
      await this.cacheUser(regNumber, email, role, otp);
    } catch (error) {
      if (error.message?.includes('NOT_FOUND')) {
        this.exceptionHandler.throwNotFound(_404.USER_NOT_FOUND);
      }
      throw error;
    }
  }

  async onboardTeacherByCodeAndEmail(
    dto: OnboardUserDto,
    isHeadOfDepartment: boolean,
  ) {
    try {
      const otp = await this.brainService.generateOTP(dto.registrationNumber);
      await this.cacheUser(
        dto.registrationNumber,
        dto.email,
        isHeadOfDepartment ? EUserRole.HoD : EUserRole.LECTURE,
        otp,
      );
    } catch (error) {
      if (error.message?.includes('NOT_FOUND')) {
        this.exceptionHandler.throwNotFound(_404.ONBOARDED_USER_NOT_FOUND);
      }
      throw error;
    }
  }

  getEducationLevelByYearOfStudy(yearOfStudy: string) {
    switch (yearOfStudy) {
      case '1':
      case '2':
        return EducationLevel.LEVEL_SIX;
      case '3':
        return EducationLevel.LEVEL_SEVEN;
      case '4':
        return EducationLevel.BTEC;
      default:
        return null;
    }
  }
  async getMisInfo(otp: number, registrationNumber: string) {
    const isValidOtp = await this.brainService.verifyOTP(
      registrationNumber,
      otp,
    );
    // if (!isValidOtp) this.exceptionHandler.throwBadRequest(_400.INVALID_OTP);

    const misInfo: UserIntegrationResponseDto =
      await this.findCachedUser(registrationNumber);

    const userEntity = await this.getUserEntity(misInfo.role, misInfo);

    userEntity.role =
      misInfo.role == EUserRole.ALUMNI ? EUserRole.STUDENT : misInfo.role;
    userEntity.graduationYear = misInfo.graduationYear;

    userEntity.educationLevel =
      misInfo.role == EUserRole.ALUMNI
        ? EducationLevel.ALUMNI
        : this.getEducationLevelByYearOfStudy(misInfo.educationLevel);
    const [createdUser] = await Promise.all([
      this.userRepository.save(userEntity),
    ]);
    return plainToClass(User, createdUser);
  }

  private async findCachedUser(registrationNumber: string) {
    const cacheKey = this.brainService.getCacheKey(registrationNumber);
    const cachedUser =
      await this.brainService.remindMe<UserIntegrationResponseDto>(cacheKey);
    if (!cachedUser)
      this.exceptionHandler.throwNotFound(_404.MIS_INFO_NOT_FOUND);
    if (cachedUser) return cachedUser;
  }

  async findAllMISUsers(searchKeyword: string = '') {
    return this.userIntegrationService.findAllUsers({
      searchKey: searchKeyword,
    });
  }

  async cacheUser(
    registrationNumber: string,
    email: string,
    role: EUserRole,
    otp?: number,
  ) {
    const cacheKey = this.brainService.getCacheKey(registrationNumber);
    let userObservable;

    if (role == EUserRole.STUDENT) {
      userObservable =
        await this.integrationStudentService.findStudentByEmailAndRegistationNumber(
          {
            registrationNumber: registrationNumber,
            email: email,
          },
        );
    } else if (role === EUserRole.LECTURE) {
      userObservable = await this.integrationTeacherService.findTeacher({
        registrationNumber: registrationNumber,
        email: email,
      });
    } else if (role === EUserRole.HoD) {
      userObservable = await this.integrationTeacherService.findHod({
        registrationNumber: registrationNumber,
        email: email,
      });
    } else {
      userObservable =
        await this.integrationStudentService.findGraduateByRegstrationNumber({
          registrationNumber: registrationNumber,
        });
    }

    const user = await lastValueFrom(
      userObservable as unknown as Observable<UserIntegrationResponseDto>,
    );

    if (!user) this.exceptionHandler.throwNotFound(_404.USER_NOT_FOUND);
    const isTeacherHod = user?.isHod === '1';

    if (role == EUserRole.HoD && !isTeacherHod) {
      this.exceptionHandler.throwNotFound(_400.USER_NOT_HOD);
    }
    if (role === EUserRole.LECTURE && isTeacherHod) {
      this.exceptionHandler.throwNotFound(_400.USER_NOT_TEACHER);
    }

    user.role = role;
    await this.brainService.memorize<UserIntegrationResponseDto>(
      cacheKey,
      user,
      USER_BY_ID_CACHE.ttl,
    );

    await this.notificationProcessor.sendTemplateEmail(
      EmailTemplates.USER_ONBOARDING_VERIFICATION,
      [email],
      {
        userName: user?.firstName ? user.firstName : email,
        otp: otp,
        otpValidityDuration: 12,
        verificationUrl: `${this.config.clientUrl}user/onboarding/verify-email/?otp=${otp}&registration-number=${registrationNumber}`,
      },
    );
  }

  async getUserEntity<
    T extends {
      telephone?: string;
      phone?: string;
      nationality?: string;
      nationalId?: string;
      firstName?: string;
      lastName?: string;
      registrationNumber?: string;
      gender: string;
      email?: string;
      dateOfBirth?: string;
      educationLevel?: string;
      graduationYear?: string;
      academicYear?: string;
      misDepartment?: DepartmentIntegration;
      misCollege?: CollegeIntegration;
      college?: CollegeIntegration;
    },
  >(role: EUserRole, entity: T): Promise<User> {
    const userEntity: User = this.userRepository.create({
      firstName: null,
      lastName: null,
    });

    userEntity.registrationNumber =
      role === EUserRole.LECTURE || role === EUserRole.HoD
        ? entity.nationalId
        : entity.registrationNumber;

    userEntity.role = role;
    userEntity.email = entity.email;
    userEntity.status = EUserStatus.ACTIVE;
    userEntity.country = entity.nationality;
    if (entity.gender) {
      userEntity.gender =
        entity.gender.toLocaleLowerCase() == 'male'
          ? EGender.MALE
          : EGender.FEMALE;
    }
    userEntity.phoneNumber = entity.telephone;
    userEntity.graduationYear = entity.graduationYear;
    userEntity.academicYear = entity.academicYear;
    userEntity.nationalId =
      role === EUserRole.LECTURE || role === EUserRole.HoD
        ? entity.nationalId
        : null;
    const misDepartment = entity.misDepartment;
    let userDepartment: Department;

    if (misDepartment) {
      const departmentExists = await this.departmentService.existsByName(
        misDepartment.departmentName,
      );
      if (departmentExists) {
        userDepartment = await this.departmentService.findByName(
          misDepartment.departmentName,
        );
      } else {
        userDepartment = await this.departmentService.create(
          misDepartment.departmentName,
          misDepartment.type,
        );
      }
    }
    let college;
    const misCollege = entity.misCollege || entity.college;
    if (misCollege) {
      const isCollegeOnboarded = await this.collegeService.existsByName(
        misCollege.shortName,
      );

      college = isCollegeOnboarded
        ? await this.collegeService.findByName(misCollege.shortName)
        : await this.collegeService.create({
            name: misCollege.shortName,
          });
      if (!isCollegeOnboarded) {
        college = await this.collegeService.getCollegeRepository().save({
          name: misCollege.shortName,
        });
      }
    }

    userEntity.department = userDepartment;
    userEntity.educationLevel = entity.educationLevel;
    userEntity.dateOfBirth = moment(entity.dateOfBirth).utc().toDate();
    userEntity.college = college;
    userEntity.firstName = entity.firstName ?? '';
    userEntity.lastName = entity.lastName ?? '';
    return this.userRepository.create(userEntity);
  }

  async confirmUserProfile(confirmProfileDto: ConfirmUserProfileDto) {
    const user = await this.findOne(confirmProfileDto.userId);
    if (!user) {
      this.exceptionHandler.throwNotFound(_404.USER_NOT_FOUND);
    }
    user.password = await hashPassword(confirmProfileDto.password);
    user.status = EUserStatus.ACTIVE;

    const [profile, updatedUser] = await Promise.all([
      this.portfolioService.createPortfolio(user),
      this.userRepository.save(user),
    ]);
    if (user.role == EUserRole.STUDENT) {
      const industrialAttachmentsObservable =
        await this.attachmentsIntegrationService.findAllByStudent({
          registrationNumber: user.registrationNumber,
        });
      const misIndustrialAttachmentResponse = await lastValueFrom(
        industrialAttachmentsObservable as unknown as Observable<AttachmentResponnse>,
      );
      const industrialAttachmentsDtos: IndustrialAttachmentDTO[] = [];

      if (misIndustrialAttachmentResponse?.attachments?.length > 0) {
        misIndustrialAttachmentResponse.attachments.forEach(
          async (attachment) => {
            const industrialAttachmentDto = new IndustrialAttachmentDTO();
            industrialAttachmentDto.academicYear = attachment.academicYear;
            industrialAttachmentDto.startDate = attachment.startDate;
            industrialAttachmentDto.endDate = attachment.endDate;
            industrialAttachmentDto.company = attachment.company;
            industrialAttachmentsDtos.push(industrialAttachmentDto);
          },
        );
        profile.industrialAttachments = industrialAttachmentsDtos;
        await Promise.all([
          this.portfolioService.saveExistingPortfolio(profile),
        ]);
      }
    }
    await this.auditLogService.create(
      user,
      EAuditLogAction.ACCOUNT_MODIFICATION,
    );
    return updatedUser;
  }

  async create(createUserDto: CreateUserDTO, isCompanyAdmin: boolean = false) {
    let college: College;
    let department: Department;
    let user: User;

    const collegeName = createUserDto.collegeName;
    const departmentName = createUserDto.departmentName;

    if (await this.existByEmail(createUserDto.email)) {
      this.exceptionHandler.throwConflict(_409.USER_ALREADY_EXISTS);
    }
    const userEntity: User = this.userRepository.create({
      firstName: createUserDto.firstName ?? '',
      lastName: createUserDto.lastName ?? '',
      email: createUserDto.email,
      phoneNumber: createUserDto.phoneNumber,
      role: createUserDto.role,
    });
    userEntity.password = await bcrypt.hash(
      this.configService.defaultPassword,
      10,
    );
    userEntity.passwordResetRequired = true;

    if (isCompanyAdmin) {
      user = await this.userRepository.save(userEntity);
      return plainToClass(User, user);
    }

    if (createUserDto.collegeName) {
      const isCollegeOnboarded =
        await this.collegeService.existsByName(collegeName);

      college = isCollegeOnboarded
        ? await this.collegeService.findByName(collegeName)
        : await this.collegeService.create({ name: collegeName });
    }

    if (createUserDto.departmentName) {
      const isDepartmentOnboarded =
        await this.departmentService.existsByName(departmentName);

      department = isDepartmentOnboarded
        ? await this.departmentService.findByName(departmentName)
        : await this.departmentService.create(departmentName, '');
    }

    userEntity.college = college;
    userEntity.department = department;
    userEntity.role = createUserDto.role;
    userEntity.passwordResetRequired = true;
    const defaultPassword = generateAlphaNumericCode(8);
    userEntity.password = await hashPassword(defaultPassword);
    let emailContent = 'You now have access on RP E-portfolio';
    if (createUserDto.role === EUserRole.INCUBATOR) {
      emailContent = `You have been added as an incubation center representative in RP E-portfolio`;
    } else if (createUserDto.role === EUserRole.QA) {
      emailContent =
        'You have been added as a quality assurance representative in RP E-portfolio';
    } else if (createUserDto.role === EUserRole.INSTITUTIONAL_ADMIN) {
      emailContent =
        'You have been added as an institutional admin in RP E-portfolio';
    }

    await Promise.all([
      this.userRepository.save(userEntity),
      this.notificationProcessor.sendTemplateEmail(
        EmailTemplates.WELCOME_COMPANY_ADMIN,
        [userEntity.email],
        {
          userName: userEntity.firstName,
          isNewUser: true,
          dashboardUrl: `${this.configService.clientUrl}/auth/login`,
          defaultPassword: defaultPassword,
          emailContent: emailContent,
        },
      ),
    ]);
    return plainToClass(User, user);
  }

  async createAdmin(createUserDto: CreateAdminDTO) {
    if (
      createUserDto.adminRegistrationCode !=
      this.configService.adminSecurityCode
    ) {
      this.exceptionHandler.throwBadRequest(_400.INVALID_ADMIN_REG_CODE);
    }
    const userExists: boolean = await this.existByEmail(createUserDto.email);
    if (userExists)
      this.exceptionHandler.throwConflict(_409.USER_ALREADY_EXISTS);
    let user: User = this.userRepository.create({
      firstName: createUserDto.firstName,
      lastName: createUserDto.lastName,
      email: createUserDto.email,
      phoneNumber: createUserDto.phoneNumber,
    });

    user.role = EUserRole.SUPER_ADMIN;
    const defaultPassword = this.configService.defaultPassword;
    user.password = await bcrypt.hash(defaultPassword, 10);
    user = await this.userRepository.save(user);
    await this.notificationProcessor.sendTemplateEmail(
      EmailTemplates.WELCOME,
      [user.email],
      {
        title: 'Welcome to RP E-portfolio',
        body1: `Hi ${user.firstName ?? user.firstName} ${user.lastName ?? user.lastName},`,
        body2: 'You have been added as an admin in RP E-portfolio',
        buttonText: 'Activate Account',
        buttonLink: `${this.configService.clientUrl}/activate/`,
        // recipientEmail: user.email,
      },
    );
    return user;
  }

  async findAll(
    roles: EUserRole[],
    loggedInUser: User,
    statuses?: EUserStatus[],
    keyword?: string,
    page?: number,
    limit?: number,
  ) {
    const query = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.department', 'department')
      .leftJoinAndSelect('user.college', 'college')
      .where('user.deletedAt IS NULL')
      .andWhere('user.id <> :loggedInUserId', {
        loggedInUserId: loggedInUser.id,
      });

    if (roles && roles.length > 0) {
      query.andWhere('user.role IN (:...roles)', { roles: roles });
    }
    if (statuses && statuses.length > 0) {
      query.andWhere('user.status IN (:...statuses)', { statuses: statuses });
    }

    if (keyword) {
      query.andWhere(
        '(user.firstName ILIKE :keyword OR user.lastName ILIKE :keyword OR user.email ILIKE :keyword)',
        { keyword: `%${keyword}%` },
      );
    }
    query.orderBy('user.updatedAt', ESortDirection.DESC);

    if (!page && !limit) {
      return await query.getMany();
    }
    const [users, total] = await query
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return createPaginatedResponse(users, total, page, limit);
  }

  async findAllByCollege(
    roles: EUserRole[],
    user?: User,
    statuses?: EUserStatus[],
    departmentIds?: string[],
    keyword?: string,
    sortColumn: string = 'user.updatedAt',
    sortDirection: ESortDirection = ESortDirection.DESC,
    page?: number,
    limit?: number,
  ) {
    const query = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.department', 'department')
      .leftJoinAndSelect('user.college', 'college');

    if (user?.college) {
      query.andWhere('college.id = :userCollegeId', {
        userCollegeId: user?.college?.id,
      });
    }

    if (roles.length > 0) {
      query.where('user.role IN (:...roles)', { roles: roles });
    }
    if (statuses && statuses?.length > 0) {
      query.andWhere('user.status IN (:...statuses)', { statuses: statuses });
    }
    if (keyword) {
      query.andWhere(
        '(user.firstName ILIKE :keyword OR user.lastName ILIKE :keyword OR user.email ILIKE :keyword)',
        { keyword: `%${keyword}%` },
      );
    }
    if (departmentIds && departmentIds?.length > 0) {
      query.andWhere('department.id IN (:...departmentIds)', {
        departmentIds: departmentIds,
      });
    }

    if (sortColumn && sortDirection) {
      query.orderBy(sortColumn, sortDirection);
    }
    let users;
    let total;

    if (page && limit) {
      [users, total] = await query
        .skip((page - 1) * limit)
        .take(limit)
        .getManyAndCount();
      return createPaginatedResponse(users, total, page, limit);
    }
    return await query.getMany();
  }

  async saveUser(user: User): Promise<User> {
    return await this.userRepository.save(user);
  }
  async findByEmail(email: string) {
    if (!(await this.existByEmail(email)))
      this.exceptionHandler.throwNotFound(_404.USER_NOT_FOUND);
    const user = await this.userRepository.findOne({
      where: { email },
      select: {
        firstName: true,
        email: true,
        lastName: true,
        role: true,
        educationLevel: true,
        password: true,
        passwordResetRequired: true,
        lastLogin: true,
        id: true,
        emailNotificationsEnabled: true,
        profilePicture: true,
        academicYear: true,
        graduationYear: true,
        department: {
          name: true,
          id: true,
        },
        college: {
          name: true,
          id: true,
        },
        status: true,
      },
      relations: ['department', 'college'],
    });
    if (user) {
      await this.auditLogService.create(user, EAuditLogAction.SYSTEM_ACCESS);
    }

    const portfolio = await this.portfolioService.findByOwner(user.id);

    return { ...user, portfolioId: portfolio?.id };
  }

  async findOne(id: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['department', 'college'],
    });
    if (!user) this.exceptionHandler.throwNotFound(_404.USER_NOT_FOUND);
    return user;
  }

  async existByEmail(email: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { email: email, deletedAt: null },
    });
    return !!user;
  }
  async existsByRegistrationNumber(registrationNumber: string) {
    const user = await this.userRepository.findOne({
      where: { registrationNumber: registrationNumber },
    });
    return !!user;
  }
  async findByRegistrationNumber(registrationNumber: string) {
    return await this.userRepository.findOne({
      where: { registrationNumber: registrationNumber },
    });
  }
  async update(id: string, updateUserDto: Partial<CreateUserDTO>) {
    const user = await this.findOne(id);
    let college: College;
    let department: Department;
    const collegeName = updateUserDto.collegeName;

    if (!user) {
      this.exceptionHandler.throwNotFound(_404.USER_NOT_FOUND);
    }
    Object.assign(user, updateUserDto);

    if (updateUserDto.role === EUserRole.COLLEGE_ADMIN) {
      const collegeAlreadyOnboarded =
        await this.collegeService.existsByName(collegeName);
      college = collegeAlreadyOnboarded
        ? await this.collegeService.findByName(collegeName)
        : await this.collegeService.create({ name: collegeName });
    }

    user.college = college;
    user.department = department;
    const updatedUser = await this.userRepository.save(user);
    await this.auditLogService.create(
      user,
      EAuditLogAction.ACCOUNT_MODIFICATION,
    );
    if (updateUserDto.email) {
      const [] = await Promise.allSettled([
        this.notificationProcessor.sendPlatformNotification({
          messageType: ENotificationMessageType.ACCOUNT_EMAIL_CHANGE,
          recipients: [{ userId: user.id }],
          subject: `Your email address was changed to ${user.email}`,
          metadata: {
            content: {
              title: 'Email Address Changed',
              description: `Your email has been updated to ${updateUserDto.email}`,
            },
          },
        }),
        this.notificationProcessor.sendTemplateEmail(
          EmailTemplates.ACCOUNT_EMAIL_CHANGE,
          [user.email],
          {
            userName: user.firstName,
            email: user.email,
          },
        ),
      ]);
    }
    return updatedUser;
  }

  async updateUserInfo(
    user: User,
    updateSettingsDto: UpdateSettingsDto,
    picture: Express.Multer.File,
  ): Promise<User> {
    user.email = updateSettingsDto.email ? updateSettingsDto.email : user.email;
    user.firstName = updateSettingsDto.firstName
      ? updateSettingsDto.firstName
      : user.firstName;
    user.lastName = updateSettingsDto.lastName
      ? updateSettingsDto.lastName
      : user.lastName;

    if (picture) {
      user.profilePicture =
        await this.minioService.getUploadedFilePath(picture);
    }

    if (user.email != updateSettingsDto.email) {
      await this.notificationProcessor.sendTemplateEmail(
        EmailTemplates.ACCOUNT_EMAIL_CHANGE,
        [user.email],
        {
          userName: user.firstName,
          email: user.email,
        },
      );
    }
    const [updatedUser] = await Promise.all([
      this.userRepository.save(user),
      // this.notificationProcessor.sendPlatformNotification({
      //   messageType: ENotificationMessageType.ACCOUNT_EMAIL_CHANGE,
      //   recipients: [{ userId: user.id }],
      //   subject: `Your email address was changed to ${user.email}`,
      //   metadata: {},
      // }),
    ]);
    await this.auditLogService.create(
      user,
      EAuditLogAction.ACCOUNT_MODIFICATION,
    );
    return updatedUser;
  }
  async updatePassword(dto: UpdateUserPasswordDto, user: User): Promise<User> {
    const passwordsMatch = await bcrypt.compare(
      dto.oldpassword.toString(),
      user.password.toString(),
    );
    if (!passwordsMatch) {
      this.exceptionHandler.throwBadRequest(_400.INVALID_OLD_PASSWORD);
    }
    user.password = await hashPassword(dto.newPassword);
    user.passwordResetRequired = false;
    const updatedUser = await this.userRepository.save(user);
    await this.auditLogService.create(user, EAuditLogAction.PASSWORD_RESET);
    await Promise.allSettled([
      this.notificationProcessor.sendTemplateEmail(
        EmailTemplates.PASSWORD_RESET,
        [user.email],
        {
          userName: user.firstName,
        },
      ),
      this.notificationProcessor.sendPlatformNotification({
        messageType: ENotificationMessageType.PASSWORD_RESET,
        recipients: [{ userId: user.id }],
        subject: `Your password was changed`,
        metadata: {
          content: {
            title: 'Your Password Was Changed',
            description: 'Your password was successfully changed',
          },
        },
      }),
    ]);
    return updatedUser;
  }
  async activateOrDeactivateTwoFactorAuth(user: User, action: EUpdateAction) {
    user.twoFactorAuthEnabled = action == EUpdateAction.ACTIVATE;
    await this.userRepository.save(user);
    await this.notificationProcessor.sendTemplateEmail(
      EmailTemplates.TWO_FACTOR_AUTH_STATUS_CHANGE,
      [user.email],
      {
        userName: user.firstName,
        action: action == EUpdateAction.ACTIVATE ? 'enabled' : 'disabled',
      },
    );
  }

  async toggleEmailNotifications(
    user: User,
    action: EUpdateAction,
  ): Promise<User> {
    user.emailNotificationsEnabled = action === EUpdateAction.ACTIVATE;
    await this.userRepository.save(user);

    if (user.emailNotificationsEnabled) {
      await this.notificationProcessor.sendTemplateEmail(
        EmailTemplates.EMAIL_NOTIFICATIONS_STATUS_CHANGE,
        [user.email],
        {
          userName: user.firstName,
          action: action === EUpdateAction.ACTIVATE ? 'enabled' : 'disabled',
        },
      );
    }

    return user;
  }

  async delete(id: string) {
    const user: User = await this.findOne(id);
    await this.userRepository.softDelete(id);
  }
  // TO DO: Remove this endpoint after engineer in need has tested
  async notifyOnPlatform(data: PlatformQueuePayload) {
    return await this.notificationProcessor.sendPlatformNotification(data);
  }
  // Example to upload a file with Minio
  async uploadFileExample(file: Express.Multer.File) {
    return this.minioService.uploadFile(file);
  }

  async findAllStudents(
    user: User,
    academicCollaborators: boolean,
    searchKeyword?: string,
  ) {
    const query = this.userRepository
      .createQueryBuilder('user')
      .where('user.role = :role', { role: EUserRole.STUDENT })
      .andWhere('user.status = :status', { status: EUserStatus.ACTIVE })
      .andWhere('user.deletedAt IS NULL');

    if (searchKeyword) {
      query.andWhere(
        '(user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search)',
        { search: `%${searchKeyword}%` },
      );
    }
    query.andWhere('user.id != :userId', { userId: user.id });
    if (academicCollaborators) {
      query.andWhere('user.educationLevel IN(:...educationLevels)', {
        educationLevels: [EducationLevel.LEVEL_SEVEN, EducationLevel.BTEC],
      });
    } else {
      query.andWhere('user.educationLevel <> :educationLevel', {
        educationLevel: EducationLevel.ALUMNI,
      });
    }

    return await query
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.educationLevel',
        'user.graduationYear',
        'user.profilePicture',
        'user.email',
        'user.role',
        'user.status',
      ])
      .getMany();
  }

  async findAllLectures(searchKeyword: string = '', user: User) {
    const query = this.userRepository
      .createQueryBuilder('user')
      .where('user.role = :role', { role: EUserRole.LECTURE })
      .andWhere('user.status = :status', { status: EUserStatus.ACTIVE })
      .andWhere('user.deletedAt IS NULL');

    if (searchKeyword) {
      query.andWhere(
        '(user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search)',
        { search: `%${searchKeyword}%` },
      );
    }
    query.andWhere('user.id != :userId', { userId: user.id });

    return await query
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.profilePicture',
        'user.email',
        'user.role',
      ])
      .getMany();
  }

  async findAllIncubationCenterStaff(searchKeyword: string = '', user: User) {
    const query = this.userRepository
      .createQueryBuilder('user')
      .where('user.role = :role', { role: EUserRole.INCUBATOR })
      .andWhere('user.status = :status', { status: EUserStatus.ACTIVE })
      .andWhere('user.deletedAt IS NULL');

    if (searchKeyword) {
      query.andWhere(
        '(user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search)',
        { search: `%${searchKeyword}%` },
      );
    }
    query.andWhere('user.id != :userId', { userId: user.id });

    return await query
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'user.status',
        'user.profilePicture',
        'user.role',
      ])
      .getMany();
  }

  async activateOrDeactivateUser(userId: string, action: EUpdateAction) {
    const user = await this.findOne(userId);
    if (!user) {
      this.exceptionHandler.throwNotFound(_404.USER_NOT_FOUND);
    }
    switch (action) {
      case EUpdateAction.ACTIVATE:
        user.status = EUserStatus.ACTIVE;
        break;
      case EUpdateAction.DEACTIVATE:
        user.status = EUserStatus.INACTIVE;
        break;
      default:
    }
    const [updatedUser, portfolio] = await Promise.all([
      this.userRepository.save(user),
      this.portfolioRepository.findOne({
        where: { user: { id: user.id } },
      }),
    ]);
    portfolio.status = EportfolioStatus.INACTIVE;
    await this.portfolioRepository.save(portfolio);

    const projectsDeactivatedStudentCollaboratedIn =
      await this.projectRepository.find({
        where: {
          collaborators: {
            user: { id: user.id },
            status: ECollaborStatus.APPROVED,
          },
        },
        relations: [
          'collaborators',
          'collaborators.user',
          'collaborators.user.college',
        ],
      });

    const collaboratorsToNotify: ProjectCollaborator[] = [];

    projectsDeactivatedStudentCollaboratedIn.forEach((project) => {
      project.collaborators.forEach(async (collaborator) => {
        if (collaborator.user.email) {
          collaboratorsToNotify.push(collaborator);
        }
      });
    });

    switch (updatedUser.role) {
      case EUserRole.STUDENT:
        if (updatedUser.status === EUserStatus.ACTIVE) {
          await this.notificationProcessor.sendTemplateEmail(
            EmailTemplates.STUDENT_ACCOUNT_REACTIVATION,
            [user.email],
            {
              studentName: user.firstName,
              deactivationDate: new Date().toLocaleDateString(),
              contactEmail: this.config.contactEmail,
              contactPhone: this.config.contactPhone,
              college: user.college.name,
            },
          );
          collaboratorsToNotify.forEach(async (collaborator) => {
            await this.notificationProcessor.sendTemplateEmail(
              EmailTemplates.NOTIFY_OWNER_ABOUT_COLLABORATOR_REACTIVATION,
              [collaborator.user.email],
              {
                collaboratorName: collaborator.user.firstName,
                studentName: user.firstName,
                deactivationDate: new Date().toLocaleDateString(),
                collegeAdminContact: this.config.contactEmail,
                college: collaborator.user.college.name,
              },
            );
          });
        }

        if (updatedUser.status === EUserStatus.INACTIVE) {
          if (user.role === EUserRole.STUDENT) {
            await Promise.all([
              this.portfolioRepository.save(portfolio),
              await this.deactivateUser(
                user,
                EmailTemplates.STUDENT_ACCOUNT_DEACTIVATION,
              ),
            ]);

            collaboratorsToNotify.forEach(async (collaborator) => {
              await this.notificationProcessor.sendTemplateEmail(
                EmailTemplates.NOTIFY_OWNER_ABOUT_COLLABORATOR_DEACTIVATION,
                [collaborator.user.email],
                {
                  collaboratorName: collaborator.user.firstName,
                  studentName: user.firstName,
                  deactivationDate: new Date().toLocaleDateString(),
                  collegeAdminContact: this.config.contactEmail,
                  college: collaborator.user.college.name,
                },
              );
            });
          }
        }
        break;
      case EUserRole.LECTURE:
        if (updatedUser.status === EUserStatus.ACTIVE) {
          await this.notificationProcessor.sendTemplateEmail(
            EmailTemplates.TEACHER_ACCOUNT_REACTIVATION,
            [user.email],
            {
              studentName: user.firstName,
              deactivationDate: new Date().toLocaleDateString(),
              contactEmail: this.config.contactEmail,
              contactPhone: this.config.contactPhone,
              college: user.college.name,
            },
          );

          for (const collaborator of collaboratorsToNotify) {
            await this.notificationProcessor.sendTemplateEmail(
              EmailTemplates.NOTIFY_STUDENT_ABOUT_SUPERVISOR_REACTIVATION,
              [collaborator.user.email],
              {
                supervisorName: user.firstName,
                studentName: collaborator.user.firstName,
                deactivationDate: new Date().toLocaleDateString(),
                collegeAdminContact: this.config.contactEmail,
                reassignLink: `${this.config.clientAppUrl}/research/projects/re-assign`,
                college: collaborator.user.college.name,
                isCollaboratorOwner:
                  collaborator.role === ECollaborationRole.OWNER,
              },
            );
          }
        }
        if (updatedUser.status === EUserStatus.INACTIVE) {
          await this.deactivateUser(
            user,
            EmailTemplates.TEACHER_ACCOUNT_DEACTIVATION,
          );

          const [
            researchProjectsDeactivatedTeacherSupervised,
            innovationProjectsDeactivatedTeacherSupervised,
          ] = await Promise.all([
            this.projectRepository.find({
              where: {
                type: EProjectType.RESEARCH,
                collaborators: {
                  user: { id: user.id, role: EUserRole.LECTURE },
                  status: ECollaborStatus.APPROVED,
                  role: ECollaborationRole.SUPERVISOR,
                },
              },
              relations: [
                'collaborators',
                'owner',
                'collaborators.user',
                'collaborators.user.college',
              ],
            }),
            await this.projectRepository.find({
              where: {
                type: EProjectType.INNOVATION,
                collaborators: {
                  user: { id: user.id, role: EUserRole.LECTURE },
                  status: ECollaborStatus.APPROVED,
                  role: ECollaborationRole.SUPERVISOR,
                },
              },
              select: ['id', 'title', 'collaborators'],
              relations: [
                'collaborators',
                'owner',
                'collaborators.user',
                'collaborators.user.college',
              ],
            }),
          ]);

          let incubationCentreStaffSupervisingInnovationProjects =
            innovationProjectsDeactivatedTeacherSupervised.map((project) => {
              const supervisor = project.collaborators.find(
                (collaborator) =>
                  collaborator.user.role === EUserRole.INCUBATOR &&
                  collaborator.status === ECollaborStatus.APPROVED &&
                  collaborator.role === ECollaborationRole.SUPERVISOR,
              );
              return supervisor?.user;
            });
          incubationCentreStaffSupervisingInnovationProjects = compact(
            incubationCentreStaffSupervisingInnovationProjects,
          );
          const hod = await this.userRepository.findOne({
            where: {
              role: EUserRole.HoD,
              college: { id: user.college.id },
              department: { id: user.department.id },
            },
          });

          const AcademicProjectsDeactivatedTeacherSupervised =
            await this.academicProjectRepository.find({
              where: {
                collaborators: {
                  user: { id: user.id, role: EUserRole.LECTURE },
                  status: ECollaborStatus.APPROVED,
                  role: ECollaborationRole.SUPERVISOR,
                },
              },
              select: ['id', 'title'],
            });

          const academicProjectTitles =
            AcademicProjectsDeactivatedTeacherSupervised.map(
              (project) => project.title,
            );

          const innovationProjectTitles =
            innovationProjectsDeactivatedTeacherSupervised.map(
              (project) => project.title,
            );
          const researchProjectTitles =
            researchProjectsDeactivatedTeacherSupervised.map(
              (project) => project.title,
            );
          const researchProjectsOwners =
            researchProjectsDeactivatedTeacherSupervised.map(
              (project) => project.owner,
            );
          for (const owner of researchProjectsOwners) {
            await this.notificationProcessor.sendTemplateEmail(
              EmailTemplates.NOTIFY_HOD_ABOUT_TEACHER_DEACTIVATION,
              [owner.email],
              {
                teacherName: user.firstName,
                deactivationDate: new Date().toLocaleDateString(),
                numberOfProjectsSupervisedByTeacher:
                  researchProjectsDeactivatedTeacherSupervised?.length.toString(),
                projectTitles: compact(researchProjectTitles),
                collegeAdminContact: this.config.contactEmail,
                college: user.college.name,
                hodName: owner?.firstName,
                reassignLink: `${this.config.clientAppUrl}/research/projects/re-assign`,
              },
            );
          }
          for (const supervisor of incubationCentreStaffSupervisingInnovationProjects) {
            await this.notifyHodAfterSupervisorDeactivation(
              supervisor,
              user,
              innovationProjectsDeactivatedTeacherSupervised.length,
              innovationProjectTitles,
            );
          }
          if (hod) {
            await this.notificationProcessor.sendTemplateEmail(
              EmailTemplates.NOTIFY_HOD_ABOUT_TEACHER_DEACTIVATION,
              [hod?.email],
              {
                teacherName: user.firstName,
                deactivationDate: new Date().toLocaleDateString(),
                numberOfProjectsSupervisedByTeacher:
                  AcademicProjectsDeactivatedTeacherSupervised.length.toString(),
                projectTitles: compact(academicProjectTitles),
                collegeAdminContact: this.config.contactEmail,
                college: user.college.name,
                hodName: hod?.firstName,
                reassignLink: `${this.config.clientAppUrl}/research/projects/re-assign`,
              },
            );
          }

          for (const collaborator of collaboratorsToNotify) {
            await this.notifyStudentAfterSupervisorDeactivation(
              collaborator,
              user,
              `${this.config.clientAppUrl}/research/projects/re-assign`,
            );
          }
        }
        break;
      case EUserRole.HoD:
        if (updatedUser.status === EUserStatus.INACTIVE) {
          await this.deactivateUser(
            user,
            EmailTemplates.HOD_ACCOUNT_DEACTIVATION,
          );
        }
        break;
      case EUserRole.INCUBATOR:
        if (updatedUser.status === EUserStatus.INACTIVE) {
          const projectsDeactivatedIncubatorSupervised =
            await this.projectRepository.find({
              where: {
                type: EProjectType.INNOVATION,
                status: EProjectStatus.SUBMITED_FOR_REVIEW,
                collaborators: {
                  user: { id: user.id, role: EUserRole.INCUBATOR },
                  status: ECollaborStatus.APPROVED,
                  role: ECollaborationRole.SUPERVISOR,
                },
              },
              relations: [
                'collaborators',
                'collaborators.user',
                'collaborators.user.college',
              ],
            });
          if (projectsDeactivatedIncubatorSupervised.length > 0) {
            for (const project of projectsDeactivatedIncubatorSupervised) {
              await this.projectRepository.update(project.id, {
                status: EProjectStatus.IN_PROGRESS,
              });
            }
            const supervisor =
              projectsDeactivatedIncubatorSupervised[0].collaborators.find(
                (collaborator) =>
                  collaborator.user.role === EUserRole.INCUBATOR &&
                  collaborator.status === ECollaborStatus.APPROVED &&
                  collaborator.role === ECollaborationRole.SUPERVISOR,
              );
            await this.notifyStudentAfterSupervisorDeactivation(
              supervisor,
              user,
              `${this.config.clientAppUrl}/research/projects/innovation`,
            );
          }
        }
        break;
      case EUserRole.QA:
        if (updatedUser.status === EUserStatus.INACTIVE) {
          await this.deactivateUser(
            user,
            EmailTemplates.QUALITY_ASSURANCE_ACCOUNT_DEACTIVATION,
          );
        }
        break;
      default:
    }

    await this.auditLogService.create(
      user,
      EAuditLogAction.ACCOUNT_MODIFICATION,
    );
    return plainToClass(User, updatedUser);
  }

  private async deactivateUser(user: User, template: EmailTemplates) {
    await this.notificationProcessor.sendTemplateEmail(template, [user.email], {
      studentName: user.firstName,
      deactivationDate: new Date().toLocaleDateString(),
      contactEmail: this.config.contactEmail,
      contactPhone: this.config.contactPhone,
      college: user.college.name,
    });
  }
  private async notifyStudentAfterSupervisorDeactivation(
    collaborator: ProjectCollaborator,
    user: User,
    reassignUrl: string,
  ) {
    await this.notificationProcessor.sendTemplateEmail(
      EmailTemplates.NOTIFY_STUDENT_ABOUT_SUPERVISOR_DEACTIVATION,
      [collaborator.user.email],
      {
        supervisorName: user.firstName,
        studentName: collaborator.user.firstName,
        deactivationDate: new Date().toLocaleDateString(),
        collegeAdminContact: this.config.contactEmail,
        college: collaborator.user.college.name,
        reassignLink: reassignUrl,
        isCollaboratorOwner: collaborator.role === ECollaborationRole.OWNER,
      },
    );
  }
  private async notifyHodAfterSupervisorDeactivation(
    supervisor: User,
    user: User,
    numberOfProjectsSupervisedByTeacher: number,
    projectTitles: string[],
  ) {
    await this.notificationProcessor.sendTemplateEmail(
      EmailTemplates.NOTIFY_HOD_ABOUT_TEACHER_DEACTIVATION,
      [supervisor.email],
      {
        teacherName: user.firstName,
        deactivationDate: new Date().toLocaleDateString(),
        numberOfProjectsSupervisedByTeacher:
          numberOfProjectsSupervisedByTeacher.toString(),
        projectTitles: compact(projectTitles),
        collegeAdminContact: this.config.contactEmail,
        college: user.college.name,
        hodName: supervisor?.firstName,
        reassignLink: `${this.config.clientAppUrl}/research/projects/re-assign`,
      },
    );
  }

  private async generateCareerMetrics(
    portfolio?: Portfolio,
    period: ETimeFrequency = ETimeFrequency.MONTHLY,
  ): Promise<CareerMetricsDto[]> {
    const metrics: CareerMetricsDto[] = [];
    const now = new Date();

    let numberOfPoints: number;
    let intervalInDays: number;

    switch (period) {
      case ETimeFrequency.WEEKLY:
        numberOfPoints = 7; // Last 7 days
        intervalInDays = 1;
        break;
      case ETimeFrequency.MONTHLY:
        numberOfPoints = 30; // Last 30 days
        intervalInDays = 1;
        break;
      case ETimeFrequency.YEARLY:
      default:
        numberOfPoints = 12; // Last 12 months
        intervalInDays = 30;
        break;
    }

    for (let i = numberOfPoints - 1; i >= 0; i--) {
      const date = new Date();
      if (period === ETimeFrequency.YEARLY) {
        date.setMonth(now.getMonth() - i);
        date.setDate(1);
      } else {
        date.setDate(now.getDate() - i * intervalInDays);
      }
      metrics.push({
        date,
        skillsAcquired: 0,
        profileViews: 0,
        jobMatches: 0,
      });
    }

    if (!portfolio) return metrics;

    const aggregateDataPoint = (timestamp: Date): number => {
      const date = new Date(timestamp);
      let index = -1;

      if (period === ETimeFrequency.YEARLY) {
        index = metrics.findIndex(
          (metric) =>
            metric.date.getMonth() === date.getMonth() &&
            metric.date.getFullYear() === date.getFullYear(),
        );
      } else {
        index = metrics.findIndex(
          (metric) => metric.date.toDateString() === date.toDateString(),
        );
      }
      return index;
    };

    portfolio.views?.forEach((view) => {
      const index = aggregateDataPoint(view.createdAt);
      if (index !== -1) {
        metrics[index].profileViews++;
      }
    });

    const jobRecommendations =
      await this.jobService.findJobRecommendationsByProfile(portfolio);
    jobRecommendations.forEach((recommendation) => {
      const index = aggregateDataPoint(recommendation.createdAt);
      if (index !== -1) {
        metrics[index].jobMatches++;
      }
    });

    const trackSkills = (items: any[], dateField: string = 'createdAt') => {
      items?.forEach((item) => {
        if (!item?.gainedSkills?.length) return;

        const itemDate = new Date(item[dateField]);
        const index = aggregateDataPoint(itemDate);
        if (index !== -1) {
          metrics[index].skillsAcquired += item.gainedSkills.length;
        }
      });
    };

    const allPortfolioSections = [
      ...(portfolio.experiences || []),
      ...(portfolio.awardsAndrecognition || []),
      ...(portfolio.extracurricularActivities || []),
      ...(portfolio.positions || []),
    ];

    trackSkills(allPortfolioSections);

    portfolio.moduleShowcases?.forEach((showcase) => {
      if (!showcase?.skills?.length) return;

      const showcaseDate = new Date(showcase.createdAt);
      const index = aggregateDataPoint(showcaseDate);
      if (index !== -1) {
        metrics[index].skillsAcquired += showcase.skills.length;
      }
    });

    return metrics;
  }

  async countProjectView(projectId: string, uniqueId: string, user: User) {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
      select: ['id', 'views', 'owner'],
    });

    if (!project) {
      throw new NotFoundException('Project not found');
    }

    project.views = project.views || [];

    const hasViewed = project.views.some((view) => view.uniqueId === uniqueId);

    if (!hasViewed) {
      const newView: ViewsDto = {
        uniqueId,
        type: EViewType.VIEW,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      project.views.push(newView);
      await this.projectRepository.save(project);
    }

    return { success: true };
  }

  async countPortfolioView(portfolioId: string, uniqueId: string, user: User) {
    const portfolio = await this.portfolioRepository.findOne({
      where: { id: portfolioId },
      select: ['id', 'views', 'user'],
    });

    if (!portfolio) {
      throw new NotFoundException('Portfolio not found');
    }

    portfolio.views = portfolio.views || [];

    const hasViewed = portfolio.views.some(
      (view) => view.uniqueId === uniqueId,
    );

    if (!hasViewed) {
      const newView: ViewsDto = {
        uniqueId,
        type: EViewType.VIEW,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      portfolio.views.push(newView);
      await this.portfolioRepository.save(portfolio);
    }

    return { success: true };
  }

  async countProjectsByOwner(owner: User) {
    const [projects, count] = await this.projectRepository.findAndCount({
      where: [
        { owner: { id: owner.id } },
        {
          collaborators: {
            user: { id: owner.id },
            status: ECollaborStatus.APPROVED,
          },
        },
      ],
    });
    return count;
  }
  async getLandingStats() {
    const projectsQuery = this.projectRepository
      .createQueryBuilder('project')
      .leftJoinAndSelect('project.owner', 'owner')
      .leftJoinAndSelect('owner.department', 'department')
      .where('project.visibility = :visibility', {
        visibility: EProjectVisibility.PUBLIC,
      })
      .select([
        'project.type',
        'project.id',
        'project.title',
        'project.thesis',
        'owner.firstName',
        'owner.lastName',
        'owner.email',
        'owner.status',
        'owner.profilePicture',
      ])
      .addSelect(
        `CASE 
          WHEN project.views IS NULL OR project.views::text = '[]' THEN 0
          ELSE (SELECT json_array_length(project.views::json))
        END`,
        'view_count',
      )
      .orderBy('"view_count"', 'DESC')
      .limit(4);

    const topTeacherPortfoliosQuery = this.portfolioRepository
      .createQueryBuilder('portfolio')
      .leftJoinAndSelect('portfolio.moduleShowcases', 'moduleShowcases')
      .leftJoinAndSelect('moduleShowcases.skills', 'skills')
      .leftJoinAndSelect('portfolio.user', 'user')
      .leftJoinAndSelect('user.department', 'department')
      .where('portfolio.status = :status', {
        status: EportfolioStatus.APPROVED,
      })
      .select([
        'portfolio.id',
        'portfolio.id',
        'portfolio.bio',
        'user.firstName',
        'user.lastName',
        'user.email',
        'user.status',
        'user.profilePicture',
        'moduleShowcases.title',
        'moduleShowcases.description',
        'moduleShowcases.cover',
        'moduleShowcases.isPublished',
        'skills',
        'department.name',
      ])
      .addSelect(
        `CASE 
          WHEN portfolio.views IS NULL OR portfolio.views::text = '[]' THEN 0
          ELSE (SELECT json_array_length(portfolio.views::json))
        END`,
        'view_count',
      )
      .orderBy('"view_count"', 'DESC')
      .limit(4);

    const topStudentsPortfoliosQuery = this.portfolioRepository
      .createQueryBuilder('portfolio')
      .leftJoinAndSelect('portfolio.moduleShowcases', 'moduleShowcases')
      .leftJoinAndSelect('moduleShowcases.skills', 'skills')
      .leftJoinAndSelect('portfolio.user', 'user')
      .leftJoinAndSelect('user.department', 'department')
      .where('portfolio.status = :status', {
        status: EportfolioStatus.APPROVED,
      })
      .select([
        'portfolio.id',
        'portfolio.bio',
        'user.firstName',
        'user.lastName',
        'user.email',
        'user.status',
        'user.profilePicture',
        'moduleShowcases.title',
        'moduleShowcases.description',
        'moduleShowcases.cover',
        'moduleShowcases.isPublished',
        'skills',
        'department.name',
      ])
      .addSelect(
        `CASE 
          WHEN portfolio.views IS NULL OR portfolio.views::text = '[]' THEN 0
          ELSE (SELECT json_array_length(portfolio.views::json))
        END`,
        'view_count',
      )
      .orderBy('"view_count"', 'DESC')
      .limit(4);

    const [projects, teacherPortfolios, studentsPortfolios] = await Promise.all(
      [
        projectsQuery.getMany(),
        topTeacherPortfoliosQuery.getMany(),
        topStudentsPortfoliosQuery.getMany(),
      ],
    );

    const enrichPortfolio = async (portfolio) => ({
      ...portfolio,
      topSkills: extractTopSkills(portfolio),
      projects: await this.countProjectsByOwner(portfolio.user),
    });

    const [
      enrichedStudentPortfolios,
      enrichedTeacherPortfolios,
      totalProjects,
      totalInnovations,
      totalPortfolios,
    ] = await Promise.all([
      Promise.all(studentsPortfolios.map(enrichPortfolio)),
      Promise.all(teacherPortfolios.map(enrichPortfolio)),
      this.countAllProjects(),
      this.countAllProjects(EProjectType.INNOVATION),
      this.portfolioService.countAllPortfolios(),
    ]);

    return {
      topProjects: projects,
      topTeacherPortfolios: enrichedTeacherPortfolios,
      topStudentPortfolios: enrichedStudentPortfolios,
      projectsCount: totalProjects,
      innovationnsCount: totalInnovations,
      portfolioCount: totalPortfolios,
    };
  }

  async countAllProjects(type?: EProjectType) {
    const query = this.projectRepository.createQueryBuilder('project');

    if (type) {
      query.where('project.type = :type', { type: type });
    }
    return await query.getCount();
  }

  async getProjectVerificationGraph(
    year: number,
    collegeId?: string,
    ownerRole?: EUserRole,
  ) {
    const endDate = new Date(year, 11, 31);
    const startDate = new Date(year, 0, 1);

    const projectsQuery = this.projectRepository
      .createQueryBuilder('project')
      .leftJoinAndSelect('project.owner', 'owner')
      .leftJoinAndSelect('owner.college', 'college')
      .where('project.created_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('project.status IN (:...statuses)', {
        statuses: ['REQUESTED_CHANGES', 'DRAFT', 'QA_REVIEW'],
      });

    if (collegeId) {
      projectsQuery.andWhere('college.id = :collegeId', { collegeId });
    }

    if (ownerRole) {
      projectsQuery.andWhere('owner.role = :ownerRole', { ownerRole });
    }

    projectsQuery
      .select('EXTRACT(MONTH FROM project.created_at)', 'month')
      .addSelect('project.status', 'status')
      .addSelect('COUNT(DISTINCT project.id)', 'count')
      .groupBy('EXTRACT(MONTH FROM project.created_at)')
      .addGroupBy('project.status');

    const results = await projectsQuery.getRawMany();

    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      month: i + 1,
      month_name: new Date(2024, i, 1).toLocaleString('en-US', {
        month: 'short',
      }),
      requested_changes: 0,
      draft: 0,
      qa_review: 0,
    }));

    results.forEach((result) => {
      const monthIndex = Math.floor(result.month) - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        switch (result.status) {
          case 'REQUESTED_CHANGES':
            monthlyData[monthIndex].requested_changes = Number(result.count);
            break;
          case 'DRAFT':
            monthlyData[monthIndex].draft = Number(result.count);
            break;
          case 'QA_REVIEW':
            monthlyData[monthIndex].qa_review = Number(result.count);
            break;
        }
      }
    });

    return monthlyData;
  }

  async findAllAllumninae(
    searchKeyword: string = '',
    user: User,
    educationLevel: string,
  ) {
    const query = this.userRepository
      .createQueryBuilder('user')
      .where('user.role = :role', { role: EUserRole.STUDENT })
      .andWhere('user.educationLevel = :educationLevel', {
        educationLevel: educationLevel,
      });

    if (searchKeyword) {
      query.andWhere(
        '(user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search)',
        { search: `%${searchKeyword}%` },
      );
    }
    query.andWhere('user.id != :userId', { userId: user.id });

    return await query
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'user.profilePicture',
        'user.status',
        'user.role',
      ])
      .getMany();
  }

  async findAllUsersToInvite(
    searchKeyword: string = '',
    loggedInUser: User,
    educationLevel: string,
    role: EUserRole,
  ) {
    const query = this.userRepository.createQueryBuilder('user');
    if (role) {
      query.where('user.role = :role', { role: role });
    }
    if (educationLevel) {
      query.andWhere('user.educationLevel = :educationLevel', {
        educationLevel: educationLevel,
      });
    }

    if (searchKeyword) {
      query.andWhere(
        '(user.email ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search)',
        { search: `%${searchKeyword}%` },
      );
    }
    query.andWhere('user.id != :userId', { userId: loggedInUser.id });

    return await query
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'user.profilePicture',
        'user.status',
        'user.role',
      ])
      .getMany();
  }
}
