import { BaseEntity } from '@app/common/database/base.entity';
import { Column, En<PERSON>ty, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { EAuditLogAction } from '../enums/audit-log-action.enum';
import { User } from '../../entities/user.entity';

@Entity('audit_log')
export class AuditLog extends BaseEntity {
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'enum', enum: EAuditLogAction })
  action: EAuditLogAction;
}
