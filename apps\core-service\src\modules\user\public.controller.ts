import { Controller, Get, Query, Param } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';
import { Public } from '@app/common/decorators/public.decorator';
import { ProjectService } from '../project/project.service';
import { EProjectType } from '../project/enums/project-type.enum';
import { PaginatedResponse } from '@app/common/dtos/pagination.response';
import { EducationLevel } from './enums/education-level.enum';
import { PortfolioService } from '../portfolio/portfolio.service';
import { TopContentResponseDto } from './dto/top-content.dto';
import { AcademicProjectService } from '../academic-project/academic-project.service';
import { EUserRole } from './enums/user-role.enum';
import { PublicService } from './public.service';
import { normalizeArray } from '@core-service/common/helpers/all.helpers';
import { EmploymentType } from '../job/enums/employment-type.enum';
import { ExperienceLevel } from '../job/enums/experience-level.enum';
import { EJobStatus } from '../job/enums/job-status.enum';
import { JobService } from '../job/job.service';

@ApiTags('Public Controller')
@Controller('public')
@Public()
export class PublicController {
  constructor(
    private readonly publicService: PublicService,
    private readonly projectService: ProjectService,
    private readonly portfolioService: PortfolioService,
    private readonly academicProjectService: AcademicProjectService,
    private readonly jobService: JobService,
  ) {}

  @Get('landing-stats')
  @ApiOperation({
    summary: 'Get featured portfolios and projects based on view count',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns top 3 portfolios and top 4 projects by view count',
    type: TopContentResponseDto,
  })
  async getFeaturedContent() {
    return await this.publicService.getLandingStats();
  }

  @Get('search')
  @ApiQuery({
    name: 'searchKeyword',
    required: false,
    type: String,
    description: 'SearchKeyword to search by',
  })
  @Public()
  async search(@Query('searchKeyword') searchKeyword: string) {
    const [projects, portfolios] = await Promise.all([
      this.projectService.searchAllProjects(searchKeyword),
      this.portfolioService.searchAllPortfolios(searchKeyword),
    ]);
    return {
      projects: projects,
      portfolios: portfolios,
    };
  }

  @Get('project')
  @ApiOperation({ summary: 'Get all published projects with filters' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'educationLevel',
    required: false,
    type: String,
    description: 'Filter by education levels',
  })
  @ApiQuery({
    name: 'projectType',
    required: false,
    type: String,
    description: 'Filter by project types',
  })
  @ApiQuery({
    name: 'departmentId',
    required: false,
    type: String,
    description: 'Filter by department IDs',
  })
  @ApiQuery({
    name: 'collegeId',
    required: false,
    type: String,
    description: 'Filter by college IDs',
  })
  @ApiQuery({
    name: 'searchKeyword',
    required: false,
    type: String,
    description: 'Search keyword',
  })
  @ApiQuery({
    name: 'ownerRole',
    required: false,
    type: String,
    description: 'Project owner',
  })
  @Public()
  async getAllPublishedProjects(
    @Query('searchKeyword') searchKeyword: string,
    @Query('projectType') projectTypes: EProjectType,
    @Query('ownerRole') ownerRole: EUserRole,
    @Query('educationLevel') educationLevels: EducationLevel,
    @Query('collegeId') collegeIds: string,
    @Query('departmentId') departmentId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResponse<any>> {
    const parsedProjectTypes = projectTypes ? [projectTypes] : [];
    const pasedEducationLevels = educationLevels ? [educationLevels] : [];
    const parsedCollegeIds = collegeIds ? [collegeIds] : [];
    const parsedDepartmentId = departmentId ? [departmentId] : [];

    return this.projectService.getAllPublishedProjects(
      searchKeyword,
      parsedProjectTypes,
      ownerRole,
      pasedEducationLevels,
      parsedDepartmentId,
      parsedCollegeIds,
      page,
      limit,
    );
  }

  @Get('project/:projectId')
  @Public()
  async viewProject(@Param('projectId') projectId: string) {
    const [academicProject, researchOrInnovationProject] = await Promise.all([
      this.academicProjectService.findProjectForPublic(projectId),
      this.projectService.findProjectForPublic(projectId),
    ]);

    if (academicProject) {
      return academicProject;
    }
    if (researchOrInnovationProject) {
      return researchOrInnovationProject;
    }
    return null;
  }

  @Get('/portfolio')
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'educationLevel', required: false })
  @ApiQuery({ name: 'departmentId', required: false })
  @ApiQuery({ name: 'collegeId', required: false })
  @ApiQuery({ name: 'searchKeyword', required: false })
  @ApiQuery({ name: 'category', required: false })
  @ApiQuery({ name: 'skills', required: false, isArray: true, type: String })
  @Public()
  async findAllProfiles(
    @Query('educationLevel') educationLevel: string,
    @Query('searchKeyword') searchKeyword: string,
    @Query('departmentId') departmentId?: string,
    @Query('collegeId') collegeId?: string,
    @Query('category') category?: string,
    @Query('skills') skills?: string[],
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const parsedSkills = normalizeArray<string>(skills);

    return this.portfolioService.findAllProfiles(
      page,
      limit,
      category,
      educationLevel,
      searchKeyword,
      parsedSkills,
      departmentId,
      collegeId,
    );
  }

  @Get('job')
  @Public()
  @ApiQuery({ name: 'keyword', required: false, type: String })
  @ApiQuery({ name: 'jobTypes', required: false, isArray: true, type: String })
  @ApiQuery({ name: 'sectors', required: false, isArray: true, type: String })
  @ApiQuery({ name: 'skills', required: false, isArray: true, type: String })
  @ApiQuery({
    name: 'experienceLevels',
    required: false,
    isArray: true,
    type: String,
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    default: EJobStatus.OPEN,
  })
  async findAllJobsForPublic(
    @Query('keyword') keyword: string,
    @Query('jobTypes') jobTypes: EmploymentType[],
    @Query('experienceLevels') experienceLevel: ExperienceLevel[],
    @Query('sectors') sectors: string[],
    @Query('skills') skills: string | string[],
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status: EJobStatus = EJobStatus.OPEN,
  ) {
    const parsedJobType = normalizeArray<EmploymentType>(jobTypes);
    const parsedSkills = Array.isArray(skills)
      ? skills
      : skills
        ? [skills]
        : [];
    const experienceLevels = normalizeArray<ExperienceLevel>(experienceLevel);

    return this.publicService.findJobsForPublic(
      {
        keyword,
        jobTypes: parsedJobType,
        sectors,
        skills: parsedSkills,
        experienceLevels,
        status,
      },
      page,
      limit,
    );
  }

  @Get('job/:id')
  @Public()
  async getJob(@Param('id') jobId: string) {
    return this.jobService.findOne(jobId);
  }
}
