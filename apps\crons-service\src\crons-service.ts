import { Modu<PERSON> } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from '@app/common/filters/exception.filters';
import { LoggerModule } from '@app/common/logger/logger.module';
import { CronsConfigModule } from './configs/crons-config.module';
import { HealthModule } from '@app/common/health/health.module';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    LoggerModule,
    CronsConfigModule,
    HealthModule,
    ScheduleModule.forRoot(),
  ],
  providers: [{ provide: APP_FILTER, useClass: AllExceptionsFilter }],
})
export class CronsModule {}
