import { Test, TestingModule } from '@nestjs/testing';
import { PublicService } from './public.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Project } from '../project/entities/project.entity';
import { Portfolio } from '../portfolio/entities/portfolio.entity';
import { Repository } from 'typeorm';
import { EProjectVisibility } from '../project/enums/project-visibility.enum';
import { EportfolioStatus } from '../portfolio/enums/portfolio-status.enum';
import { EUserRole } from './enums/user-role.enum';
import { EProjectType } from '../project/enums/project-type.enum';
import { ClientGrpc } from '@nestjs/microservices';
import { JobService } from '../job/job.service';
import { INTEGRATION_GRPC_PACKAGE } from '@app/common/constants/services-constants';

describe('PublicService', () => {
  let service: PublicService;
  let projectRepository: Repository<Project>;
  let portfolioRepository: Repository<Portfolio>;
  let jobService: JobService;

  const mockProjectRepository = {
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue([]),
      getCount: jest.fn().mockResolvedValue(0),
    })),
    findOne: jest.fn(),
    save: jest.fn(),
    count: jest.fn(),
  };

  const mockPortfolioRepository = {
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue([]),
    })),
    findOne: jest.fn(),
    save: jest.fn(),
    count: jest.fn(),
  };

  const mockJobService = {
    findJobRecommendationsByProfile: jest.fn().mockResolvedValue([]),
  };

  const mockClientGrpc = {
    getService: jest.fn().mockReturnValue({
      findOne: jest.fn(),
      findAll: jest.fn(),
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PublicService,
        {
          provide: getRepositoryToken(Project),
          useValue: mockProjectRepository,
        },
        {
          provide: getRepositoryToken(Portfolio),
          useValue: mockPortfolioRepository,
        },
        {
          provide: JobService,
          useValue: mockJobService,
        },
        {
          provide: INTEGRATION_GRPC_PACKAGE,
          useValue: mockClientGrpc,
        },
      ],
    }).compile();

    service = module.get<PublicService>(PublicService);
    projectRepository = module.get<Repository<Project>>(
      getRepositoryToken(Project),
    );
    portfolioRepository = module.get<Repository<Portfolio>>(
      getRepositoryToken(Portfolio),
    );
    jobService = module.get<JobService>(JobService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getLandingStats', () => {
    it('should return landing stats with correct structure', async () => {
      const mockProjects = [
        {
          id: '1',
          title: 'Test Project',
          type: EProjectType.RESEARCH,
          views: [{ uniqueId: '1' }],
          owner: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            profilePicture: 'picture.jpg',
          },
        },
      ];

      const mockTeacherPortfolios = [
        {
          id: '1',
          bio: 'Test Bio',
          user: {
            firstName: 'Teacher',
            lastName: 'One',
            email: '<EMAIL>',
            role: EUserRole.LECTURE,
          },
          views: [{ uniqueId: '1' }],
          moduleShowcases: [],
        },
      ];

      const mockStudentPortfolios = [
        {
          id: '2',
          bio: 'Test Bio',
          user: {
            firstName: 'Student',
            lastName: 'One',
            email: '<EMAIL>',
            role: EUserRole.STUDENT,
          },
          views: [{ uniqueId: '2' }],
          moduleShowcases: [],
        },
      ];

      mockProjectRepository
        .createQueryBuilder()
        .getMany.mockResolvedValue(mockProjects);
      mockPortfolioRepository
        .createQueryBuilder()
        .getMany.mockResolvedValueOnce(mockTeacherPortfolios)
        .mockResolvedValueOnce(mockStudentPortfolios);
      mockProjectRepository.createQueryBuilder().getCount.mockResolvedValue(10);
      mockPortfolioRepository.count.mockResolvedValue(20);

      const result = await service.getLandingStats();

      expect(result).toHaveProperty('topProjects');
      expect(result).toHaveProperty('topTeacherPortfolios');
      expect(result).toHaveProperty('topStudentPortfolios');
      expect(result).toHaveProperty('projectsCount');
      expect(result).toHaveProperty('innovationnsCount');
      expect(result).toHaveProperty('portfolioCount');
    });
  });

  describe('countProjectView', () => {
    it('should count project view for new viewer', async () => {
      const mockProject = {
        id: '1',
        views: [],
      };

      mockProjectRepository.findOne.mockResolvedValue(mockProject);
      mockProjectRepository.save.mockResolvedValue({
        ...mockProject,
        views: [{ uniqueId: 'unique1' }],
      });

      const result = await service.countProjectView('1', 'unique1', {} as any);

      expect(result).toEqual({ success: true });
      expect(mockProjectRepository.save).toHaveBeenCalled();
    });

    it('should not count duplicate views', async () => {
      const mockProject = {
        id: '1',
        views: [{ uniqueId: 'unique1' }],
      };

      mockProjectRepository.findOne.mockResolvedValue(mockProject);

      const result = await service.countProjectView('1', 'unique1', {} as any);

      expect(result).toEqual({ success: true });
      expect(mockProjectRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('countPortfolioView', () => {
    it('should count portfolio view for new viewer', async () => {
      const mockPortfolio = {
        id: '1',
        views: [],
      };

      mockPortfolioRepository.findOne.mockResolvedValue(mockPortfolio);
      mockPortfolioRepository.save.mockResolvedValue({
        ...mockPortfolio,
        views: [{ uniqueId: 'unique1' }],
      });

      const result = await service.countPortfolioView(
        '1',
        'unique1',
        {} as any,
      );

      expect(result).toEqual({ success: true });
      expect(mockPortfolioRepository.save).toHaveBeenCalled();
    });

    it('should not count duplicate views', async () => {
      const mockPortfolio = {
        id: '1',
        views: [{ uniqueId: 'unique1' }],
      };

      mockPortfolioRepository.findOne.mockResolvedValue(mockPortfolio);

      const result = await service.countPortfolioView(
        '1',
        'unique1',
        {} as any,
      );

      expect(result).toEqual({ success: true });
      expect(mockPortfolioRepository.save).not.toHaveBeenCalled();
    });
  });
});
