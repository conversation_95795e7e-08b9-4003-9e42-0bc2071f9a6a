import { Controller, Get, Query, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AuditLogService } from './audit-log.service';
import { EAuditLogAction } from './enums/audit-log-action.enum';
import { User } from '../entities/user.entity';
import { AuthUser } from '@core-service/decorators/auth.decorator';

@Controller('audit-logs')
@ApiTags('Audit Logs')
@ApiBearerAuth()
@AuthUser()
export class AuditLogController {
  constructor(private readonly auditLogService: AuditLogService) {}

  @Get()
  @ApiQuery({ name: 'action', enum: EAuditLogAction, required: false })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiQuery({ name: 'page', required: false, example: 1 })
  @ApiQuery({ name: 'limit', required: false, example: 10 })
  async findAll(
    @Req() req,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('action') action?: EAuditLogAction,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    return await this.auditLogService.findAll(
      req.user as User,
      page,
      limit,
      action,
      startDate,
      endDate,
    );
  }
}
