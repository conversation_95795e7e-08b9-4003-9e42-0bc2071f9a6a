import { IsEmail, IsEnum, <PERSON>NotEmpty, IsString } from 'class-validator';
import { EUserRole } from '../enums/user-role.enum';
import { ApiProperty } from '@nestjs/swagger';

export class ResendOtpDto {
  @IsEmail()
  @IsNotEmpty()
  @ApiProperty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  registrationNumber: string;

  @IsEnum(EUserRole)
  @IsNotEmpty()
  @ApiProperty()
  role: EUserRole;
}
